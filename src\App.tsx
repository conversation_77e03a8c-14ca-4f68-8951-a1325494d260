// import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Navigation } from './components/layout/navigation';
import { Footer } from './components/layout/footer';
import { CommandPaletteProvider } from './hooks/use-command-palette';
import { CommandPaletteModal } from './components/layout/command-palette';
import { HomePage } from './pages/home';
import { AboutPage } from './pages/about';
import { SkillsPage } from './pages/skills';
import { ProjectsPage } from './pages/projects';

import { ContactPage } from './pages/contact';

function App() {
  return (
    <CommandPaletteProvider>
      <Router>
        <div className="min-h-screen bg-black text-white dark">
          <Navigation />
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/skills" element={<SkillsPage />} />
            <Route path="/projects" element={<ProjectsPage />} />

            <Route path="/contact" element={<ContactPage />} />
          </Routes>
          <Footer />
          <CommandPaletteModal />
        </div>
      </Router>
    </CommandPaletteProvider>
  );
}

export default App;