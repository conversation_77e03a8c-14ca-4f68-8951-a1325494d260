'use client'
// Fixed React Hooks violations, positioning warnings, color format issues, and performance issues
import React, { useRef, useState, useCallback, useMemo, lazy, Suspense } from 'react'
import { motion, useScroll, useMotionValueEvent, useSpring, useTransform, useReducedMotion } from 'framer-motion'
import { ExternalLink, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ScrollLink } from '@/components/ui/scroll-link'
import { getFeaturedProjects, type FeaturedProject } from '@/data/projects'
import {
  fadeInUp,
  staggerContainer,
  staggerItem,
  transitions,
  viewportConfig,
  getAnimation,
  getTransition
} from '@/utils/animation-config'

// Lazy load heavy components for better performance
const LazyImage = lazy(() => import('@/components/ui/lazy-image'))

// Extended project type for component usage with additional computed properties
type ExtendedFeaturedProject = FeaturedProject & {
  accentColorHex: string
  accentColorClass: string
}

// Use the extended type for components
type Project = ExtendedFeaturedProject

// Enhanced project content component with performance optimizations
const ProjectContent: React.FC<{ project: Project; isActive: boolean }> = React.memo(({ project, isActive }) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const prefersReducedMotion = useReducedMotion()

  // Enhanced scroll animations with better timing and reduced motion support
  const { scrollYProgress } = useScroll({
    target: contentRef,
    offset: ["start 0.9", "end 0.1"]
  })

  // Transform values for animations
  const yTransform = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [50, 0, 0, 50])
  const opacityTransform = useTransform(scrollYProgress, [0, 0.1, 0.4, 0.9, 1], [0, 1, 1, 1, 0])
  const xTransform = useTransform(scrollYProgress, [0, 0.2], [-100, 0])

  // Spring animations
  const ySpring = useSpring(yTransform, { stiffness: 50, damping: 20 })
  const opacitySpring = useSpring(opacityTransform, { stiffness: 50, damping: 20 })
  const xSpring = useSpring(xTransform, { stiffness: 40, damping: 15 })

  // Memoized animation values for better performance
  const animationValues = useMemo(() => {
    if (prefersReducedMotion) {
      return {
        y: 0,
        opacity: isActive ? 1 : 0.6,
        x: 0
      }
    }

    return {
      y: ySpring,
      opacity: opacitySpring,
      x: xSpring
    }
  }, [prefersReducedMotion, isActive, ySpring, opacitySpring, xSpring])

  // Optimized transition configs
  const transitionConfig = useMemo(() =>
    getTransition(transitions.standard), [prefersReducedMotion])

  // Optimized viewport settings
  const optimizedViewport = useMemo(() => viewportConfig, [])

  return (
    <motion.div
      ref={contentRef}
      className="relative transition-all duration-700 ease-out will-change-transform"
      style={{
        opacity: animationValues.opacity,
        translateX: animationValues.x,
        translateY: animationValues.y
      }}
    >
      <div className="space-y-6 max-[424px]:space-y-3 mobile:space-y-3 sm:space-y-4 md:space-y-4 lg:space-y-6 max-w-lg max-[424px]:max-w-none max-[424px]:text-center min-[425px]:max-[639px]:max-w-none min-[425px]:max-[639px]:text-center">
        {/* Project category indicator */}
        <motion.div
          initial={{ opacity: 0, y: prefersReducedMotion ? 0 : 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          animate={{ opacity: isActive ? 1 : 0, y: isActive ? 0 : (prefersReducedMotion ? 0 : 20) }}
          transition={{ delay: isActive ? 0.1 : 0, ...transitionConfig }}
          viewport={viewportConfig}
        >
          <h3 className="text-3xl max-[424px]:text-xl mobile:text-xl sm:text-2xl md:text-2xl lg:text-4xl font-bold text-[#E0E0E0] mb-4 max-[424px]:mb-2 mobile:mb-2 sm:mb-3 md:mb-3 lg:mb-4 leading-tight tracking-tight">
            {project.title}
          </h3>
          <p className="text-[#888888] text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm tracking-wide leading-relaxed max-[424px]:leading-normal mobile:leading-normal">
            {project.problemStatement}
          </p>
        </motion.div>

        {/* Tech stack with optimized animations */}
        <motion.div
          initial={{ opacity: 0, y: prefersReducedMotion ? 0 : 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          animate={{ opacity: isActive ? 1 : 0, y: isActive ? 0 : (prefersReducedMotion ? 0 : 20) }}
          transition={{ delay: isActive ? 0.2 : 0, ...transitionConfig }}
          viewport={viewportConfig}
        >
          <div className="flex flex-wrap gap-2 max-[424px]:gap-1.5 mobile:gap-1.5 sm:gap-1.5 md:gap-1.5 lg:gap-2">
            {project.technologies.map((tech: string, idx: number) => (
              <motion.div
                key={tech}
                initial={{ opacity: 0, scale: prefersReducedMotion ? 1 : 0.8 }}
                animate={{
                  opacity: isActive ? 1 : 0,
                  scale: isActive ? 1 : (prefersReducedMotion ? 1 : 0.8)
                }}
                transition={{
                  delay: isActive ? (prefersReducedMotion ? 0 : 0.3 + idx * 0.05) : 0,
                  duration: prefersReducedMotion ? 0.2 : 0.4
                }}
              >
                <span
                  className="inline-flex items-center px-3 py-1.5 max-[424px]:px-2 max-[424px]:py-1 mobile:px-2 mobile:py-1 sm:px-2 sm:py-1 md:px-2 md:py-1 lg:px-3 lg:py-1.5 bg-[#111111]/30 border border-[#333333] rounded-full text-[#E0E0E0] text-xs max-[424px]:text-[10px] mobile:text-[10px] sm:text-[10px] md:text-[10px] lg:text-xs font-medium backdrop-blur-sm transition-all duration-300 cursor-default will-change-transform hover:bg-opacity-10"
                  style={{
                    '--accent-color': project.accentColorHex
                  } as React.CSSProperties}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = `${project.accentColorHex}80`
                    e.currentTarget.style.color = project.accentColorHex
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#333333'
                    e.currentTarget.style.color = '#E0E0E0'
                  }}
                >
                  {tech}
                </span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Features list with optimized animations */}
        <motion.div
          initial={{ opacity: 0, y: prefersReducedMotion ? 0 : 20 }}
          animate={{ opacity: isActive ? 1 : 0, y: isActive ? 0 : (prefersReducedMotion ? 0 : 20) }}
          transition={{ delay: isActive ? 0.4 : 0, ...transitionConfig }}
        >
          <ul className="space-y-3 max-[424px]:space-y-2 mobile:space-y-2 sm:space-y-2 md:space-y-2 lg:space-y-3">
            {project.features.map((feature: string, idx: number) => (
              <motion.li
                key={`${project.title}-feature-${idx}`}
                className="flex items-start gap-3 max-[424px]:gap-2 mobile:gap-2 sm:gap-2 md:gap-2 lg:gap-3 text-[#B0B0B0]"
                initial={{ opacity: 0, x: prefersReducedMotion ? 0 : -20 }}
                animate={{
                  opacity: isActive ? 1 : 0,
                  x: isActive ? 0 : (prefersReducedMotion ? 0 : -20)
                }}
                transition={{
                  delay: isActive ? (prefersReducedMotion ? 0 : 0.5 + idx * 0.1) : 0,
                  duration: prefersReducedMotion ? 0.2 : 0.5
                }}
              >
                <div
                  className="w-1.5 h-1.5 max-[424px]:w-1 max-[424px]:h-1 mobile:w-1 mobile:h-1 sm:w-1 sm:h-1 md:w-1 md:h-1 lg:w-1.5 lg:h-1.5 rounded-full flex-shrink-0 mt-2 max-[424px]:mt-1.5 mobile:mt-1.5 sm:mt-1.5 md:mt-1.5 lg:mt-2"
                  style={{ backgroundColor: project.accentColorHex }}
                />
                <span className="text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm leading-relaxed max-[424px]:leading-normal mobile:leading-normal">{feature}</span>
              </motion.li>
            ))}
          </ul>
        </motion.div>

        {/* Action buttons with enhanced accessibility */}
        <motion.div
          className="flex max-[424px]:flex-col mobile:flex-col gap-4 max-[424px]:gap-3 mobile:gap-3 sm:gap-3 md:gap-3 lg:gap-4 pt-4 max-[424px]:pt-3 mobile:pt-3 sm:pt-3 md:pt-3 lg:pt-4"
          initial={{ opacity: 0, y: prefersReducedMotion ? 0 : 20 }}
          animate={{ opacity: isActive ? 1 : 0, y: isActive ? 0 : (prefersReducedMotion ? 0 : 20) }}
          transition={{ delay: isActive ? 0.6 : 0, ...transitionConfig }}
        >
          <Button
            className="relative group overflow-hidden bg-[#2A2A2A] hover:bg-[#333333] text-white font-medium px-6 py-3 max-[424px]:px-4 max-[424px]:py-2.5 mobile:px-4 mobile:py-2.5 sm:px-4 sm:py-2 md:px-4 md:py-2 lg:px-6 lg:py-3 rounded-xl transition-all duration-300 hover:scale-105 border border-[#444444] hover:border-[#555555] will-change-transform focus:ring-2 focus:ring-mono-accent focus:ring-offset-2 focus:ring-offset-mono-bg"
            asChild
          >
            <a
              href={project.liveUrl}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={`View live demo of ${project.title}`}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="relative flex items-center justify-center max-[424px]:justify-center mobile:justify-center">
                <ExternalLink className="w-4 h-4 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3 sm:h-3 md:w-3 md:h-3 lg:w-4 lg:h-4 mr-2 max-[424px]:mr-1.5 mobile:mr-1.5 sm:mr-1.5 md:mr-1.5 lg:mr-2" aria-hidden="true" />
                <span className="text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm">Live Demo</span>
              </div>
            </a>
          </Button>
          <Button
            variant="outline"
            className="bg-transparent border border-[#333333] text-[#E0E0E0] hover:bg-[#1A1A1A] hover:border-[#444444] px-6 py-3 max-[424px]:px-4 max-[424px]:py-2.5 mobile:px-4 mobile:py-2.5 sm:px-4 sm:py-2 md:px-4 md:py-2 lg:px-6 lg:py-3 rounded-xl transition-all duration-300 hover:scale-105 will-change-transform focus:ring-2 focus:ring-mono-accent focus:ring-offset-2 focus:ring-offset-mono-bg"
            asChild
          >
            <a
              href={project.githubUrl}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={`View source code for ${project.title}`}
            >
              <ExternalLink className="w-4 h-4 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3 sm:h-3 md:w-3 md:h-3 lg:w-4 lg:h-4 mr-2 max-[424px]:mr-1.5 mobile:mr-1.5 sm:mr-1.5 md:mr-1.5 lg:mr-2" aria-hidden="true" />
              <span className="text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm">View Code</span>
            </a>
          </Button>
        </motion.div>
      </div>
    </motion.div>
  )
})

// Optimized project card component with lazy loading and performance enhancements
const ProjectCard: React.FC<{ project: Project; isActive: boolean }> = React.memo(({ project, isActive }) => {
  const cardRef = useRef<HTMLDivElement>(null)
  const prefersReducedMotion = useReducedMotion()

  // Enhanced scroll animations with better timing and reduced motion support
  const { scrollYProgress } = useScroll({
    target: cardRef,
    offset: ["start 0.9", "end 0.1"]
  })

  // Transform values for animations
  const yTransformCard = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [50, 0, 0, 50])
  const opacityTransformCard = useTransform(scrollYProgress, [0, 0.1, 0.4, 0.9, 1], [0, 1, 1, 1, 0])
  const xTransformCard = useTransform(scrollYProgress, [0, 0.2], [100, 0])

  // Spring animations
  const ySpringCard = useSpring(yTransformCard, { stiffness: 50, damping: 20 })
  const opacitySpringCard = useSpring(opacityTransformCard, { stiffness: 50, damping: 20 })
  const xSpringCard = useSpring(xTransformCard, { stiffness: 40, damping: 15 })

  // Memoized animation values for better performance
  const animationValues = useMemo(() => {
    if (prefersReducedMotion) {
      return {
        y: 0,
        opacity: isActive ? 1 : 0.6,
        x: 0
      }
    }

    return {
      y: ySpringCard,
      opacity: opacitySpringCard,
      x: xSpringCard
    }
  }, [prefersReducedMotion, isActive, ySpringCard, opacitySpringCard, xSpringCard])

  // Memoized hover animations
  const hoverAnimations = useMemo(() => ({
    card: prefersReducedMotion ? {} : {
      y: -12,
      transition: { duration: 0.4, ease: [0.4, 0, 0.2, 1] as const }
    },
    image: prefersReducedMotion ? {} : {
      scale: 1.05,
      transition: { duration: 0.4, ease: [0.4, 0, 0.2, 1] as const }
    }
  }), [prefersReducedMotion])

  return (
    <motion.div
      ref={cardRef}
      className="relative flex-1 flex items-center justify-center min-h-[65vh] max-[424px]:min-h-0 mobile:min-h-0 sm:min-h-[45vh] md:min-h-[50vh] lg:min-h-[65vh]"
      style={{
        opacity: animationValues.opacity,
        translateX: animationValues.x,
        translateY: animationValues.y
      }}
    >
      <motion.div
        className={`relative bg-gradient-to-br ${project.cardStyle.background} backdrop-blur-sm rounded-3xl max-[424px]:rounded-2xl mobile:rounded-2xl sm:rounded-2xl md:rounded-2xl lg:rounded-3xl p-8 max-[424px]:p-4 mobile:p-4 sm:p-5 md:p-6 lg:p-8 shadow-2xl overflow-hidden max-w-lg w-full transition-all duration-700 transform-gpu will-change-transform ${
          isActive ? 'scale-100 opacity-100' : 'scale-95 opacity-60'
        }`}
        animate={{
          scale: isActive ? 1 : (prefersReducedMotion ? 1 : 0.95),
          opacity: isActive ? 1 : 0.6,
        }}
        transition={{
          duration: prefersReducedMotion ? 0.2 : 0.8,
          ease: [0.4, 0, 0.6, 1] as const
        }}
        whileHover={hoverAnimations.card}
      >
        {/* Project content with enhanced accessibility */}
        <div className="relative z-10 mb-8 max-[424px]:mb-2 mobile:mb-2 sm:mb-5 md:mb-6 lg:mb-8">
          <p className="text-mono-text/95 text-base max-[424px]:text-sm mobile:text-sm sm:text-sm md:text-sm lg:text-base leading-relaxed max-[424px]:leading-normal mobile:leading-normal font-medium">
            {project.description}
          </p>
          <motion.div
            className="mt-4 max-[424px]:mt-3 mobile:mt-3 sm:mt-3 md:mt-3 lg:mt-4 flex items-center gap-3 max-[424px]:gap-2 mobile:gap-2 sm:gap-2 md:gap-2 lg:gap-3 text-mono-secondary group cursor-pointer"
            whileHover={prefersReducedMotion ? {} : { x: 8 }}
            transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] as const }}
            role="button"
            tabIndex={0}
            aria-label={`Explore ${project.title} project details`}
          >
            <motion.div
              animate={prefersReducedMotion ? {} : { x: [0, 5, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <ArrowRight className="w-4 h-4 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3 sm:h-3 md:w-3 md:h-3 lg:w-4 lg:h-4" aria-hidden="true" />
            </motion.div>
            <span className="text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm font-medium">Explore Project</span>
          </motion.div>
        </div>

        {/* Project image with lazy loading and enhanced accessibility */}
        <motion.div
          className="relative z-20"
          whileHover={hoverAnimations.image}
        >
          <div className="relative rounded-2xl max-[424px]:rounded-xl mobile:rounded-xl sm:rounded-xl md:rounded-xl lg:rounded-2xl overflow-hidden shadow-xl ring-1 ring-white/10">
            <Suspense fallback={
              <div className="w-full h-64 max-[424px]:h-32 mobile:h-32 sm:h-40 md:h-48 lg:h-64 bg-[#1a1a1a] animate-pulse rounded-2xl max-[424px]:rounded-xl mobile:rounded-xl sm:rounded-xl md:rounded-xl lg:rounded-2xl flex items-center justify-center">
                <span className="text-[#666666] text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm">Loading...</span>
              </div>
            }>
              <LazyImage
                src={project.image}
                alt={project.imageAlt}
                className="w-full h-64 max-[424px]:h-32 mobile:h-32 sm:h-40 md:h-48 lg:h-64 object-cover"
                onLoad={() => {
                  // Image loaded successfully
                }}
              />
            </Suspense>
            <motion.div
              className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-0"
              whileHover={prefersReducedMotion ? {} : { opacity: 1 }}
              transition={{ duration: 0.3 }}
            />
          </div>
        </motion.div>

        {/* Enhanced accent elements with reduced motion support */}
        <motion.div
          className="absolute top-6 right-6 max-[424px]:top-3 max-[424px]:right-3 mobile:top-3 mobile:right-3 sm:top-4 sm:right-4 md:top-4 md:right-4 lg:top-6 lg:right-6 w-2 h-2 max-[424px]:w-1.5 max-[424px]:h-1.5 mobile:w-1.5 mobile:h-1.5 sm:w-1.5 sm:h-1.5 md:w-1.5 md:h-1.5 lg:w-2 lg:h-2 rounded-full"
          style={{ backgroundColor: project.accentColorHex }}
          animate={prefersReducedMotion ? {} : {
            scale: [1, 1.3, 1],
            opacity: [0.4, 0.8, 0.4],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: [0.4, 0, 0.6, 1] as const
          }}
          aria-hidden="true"
        />
      </motion.div>
    </motion.div>
  )
})

export function FeaturedProjectsSection() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [activeProject, setActiveProject] = useState(0)
  const prefersReducedMotion = useReducedMotion()

  // Mobile detection for better animation handling
  const [isMobile, setIsMobile] = useState(false)

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Get featured projects from centralized data with proper hook usage
  const featuredProjects = useMemo(() => {
    return getFeaturedProjects().map(project => ({
      ...project,
      // Keep the hex color for inline styles to avoid Tailwind purging issues
      accentColorHex: project.accentColor,
      // Also provide Tailwind classes for static usage
      accentColorClass: project.accentColor === '#00FF85' ? 'bg-green-400' :
                        project.accentColor === '#1E90FF' ? 'bg-blue-400' :
                        project.accentColor === '#FF0099' ? 'bg-pink-400' : 'bg-gray-400'
    }))
  }, [])

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start 0.8", "end 0.2"]
  })

  // Memoized callback for better performance
  const handleScrollProgress = useCallback((latest: number) => {
    const projectIndex = Math.min(
      Math.floor(latest * featuredProjects.length),
      featuredProjects.length - 1
    )
    setActiveProject(projectIndex)
  }, [featuredProjects.length])

  useMotionValueEvent(scrollYProgress, "change", handleScrollProgress)

  // Memoized background animations with mobile optimization
  const backgroundAnimations = useMemo(() => {
    if (prefersReducedMotion || isMobile) {
      return {
        first: {},
        second: {}
      }
    }

    return {
      first: {
        y: [-30, 30],
        opacity: [0.15, 0.05],
        scale: [1, 1.2, 1],
        transition: {
          duration: 12,
          repeat: Infinity,
          repeatType: "reverse" as const,
          ease: [0.4, 0, 0.6, 1] as const
        }
      },
      second: {
        y: [30, -30],
        opacity: [0.05, 0.15],
        scale: [1.2, 1, 1.2],
        transition: {
          duration: 15,
          repeat: Infinity,
          repeatType: "reverse" as const,
          ease: [0.4, 0, 0.6, 1] as const
        }
      }
    }
  }, [prefersReducedMotion, isMobile])

  return (
    <section
      id="featured-projects"
      className="relative bg-mono-bg overflow-hidden py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32"
      ref={containerRef}
      aria-labelledby="featured-projects-heading"
    >
      {/* Enhanced background effects with parallax and reduced motion support */}
      <div className="absolute inset-0" aria-hidden="true">
        <motion.div
          className="absolute top-1/4 left-1/4 w-[800px] h-[800px] bg-mono-surface/8 rounded-full blur-[150px] will-change-transform"
          animate={backgroundAnimations.first}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-[1000px] h-[1000px] bg-mono-surface-light/8 rounded-full blur-[180px] will-change-transform"
          animate={backgroundAnimations.second}
        />
      </div>

      {/* Section header with consistent spacing and enhanced accessibility - Match beyond-the-code section content width */}
      <div className="relative z-10 max-w-4xl mx-auto px-8 max-[424px]:px-8 mobile:px-8 sm:px-8 md:px-8 lg:px-6 mb-28 max-[424px]:mb-12 mobile:mb-12 sm:mb-16 md:mb-16 lg:mb-28 max-[424px]:max-w-4xl mobile:max-w-4xl">
        <motion.div
          initial={{ opacity: 0, y: prefersReducedMotion ? 0 : 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: prefersReducedMotion ? 0.2 : 0.8 }}
          viewport={{ once: true, margin: "-10% 0px -10% 0px", amount: 0.3 }}
          className="space-y-3 max-[424px]:space-y-2 mobile:space-y-2 text-center"
        >
          <p className="text-mono-accent text-sm max-[424px]:text-xs mobile:text-xs font-medium tracking-widest uppercase">
            Curated Work
          </p>
          <h2
            id="featured-projects-heading"
            className="text-4xl max-[424px]:text-3xl mobile:text-3xl sm:text-4xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight tracking-tight"
          >
            Project{' '}
            <span className="relative inline-block">
              <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                Highlights
              </span>
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-20"
                animate={prefersReducedMotion ? {} : {
                  opacity: [0.2, 0.3, 0.2],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                aria-hidden="true"
              />
            </span>
          </h2>
        </motion.div>
      </div>

      {/* Project cards with enhanced spacing, animations, and accessibility - Match beyond-the-code section content width */}
      <div className="relative max-w-4xl mx-auto px-8 max-[424px]:px-8 mobile:px-8 sm:px-8 md:px-8 lg:px-6 max-[424px]:max-w-4xl mobile:max-w-4xl">
        {featuredProjects.map((project: Project, index: number) => (
          <article
            key={`project-${index}`}
            className="relative flex max-[424px]:flex-col mobile:flex-col sm-tablet:flex-row items-center gap-20 max-[424px]:gap-4 mobile:gap-4 sm-tablet:gap-8 md:gap-12 lg:gap-20 py-24 max-[424px]:py-16 mobile:py-16 sm-tablet:py-12 md:py-16 lg:py-24 first:pt-0 last:pb-0"
            aria-labelledby={`project-title-${index}`}
          >
            {/* Left side content with enhanced animations */}
            <div className="relative flex-shrink-0 w-2/5 max-[424px]:w-full mobile:w-full sm-tablet:w-2/5 flex items-center max-[424px]:justify-center mobile:justify-center sm-tablet:justify-start min-h-[65vh] max-[424px]:min-h-0 mobile:min-h-0 sm-tablet:min-h-[45vh] md:min-h-[50vh] lg:min-h-[65vh]">
              <ProjectContent
                project={project}
                isActive={activeProject === index}
              />
            </div>

            {/* Right side - Project card with refined animations */}
            <div className="max-[424px]:w-full max-[424px]:max-w-sm max-[424px]:mx-auto min-[425px]:max-[639px]:w-full min-[425px]:max-[639px]:max-w-sm min-[425px]:max-[639px]:mx-auto sm-tablet:w-auto sm-tablet:max-w-none sm-tablet:mx-0">
              <ProjectCard
                project={project}
                isActive={activeProject === index}
              />
            </div>
          </article>
        ))}
      </div>

      {/* Enhanced CTA Section with updated styling and accessibility */}
      <div className="relative z-10 text-center mt-16">
        <motion.div
          initial={{ opacity: 0, y: prefersReducedMotion ? 0 : 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: prefersReducedMotion ? 0.2 : 0.8 }}
          viewport={{ once: true, margin: "-10% 0px -10% 0px", amount: 0.3 }}
        >
          <ScrollLink
            to="/projects"
            className="group relative overflow-hidden inline-flex items-center gap-3 bg-transparent border-2 border-mono-border/30 hover:border-mono-accent/50 text-[#E0E0E0] font-medium px-6 py-3 rounded-xl transition-all duration-500 will-change-transform focus:ring-2 focus:ring-mono-accent focus:ring-offset-2 focus:ring-offset-mono-bg"
            aria-label="View all projects in portfolio"
          >
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] to-[#8B31CD] opacity-0 group-hover:opacity-5 transition-opacity duration-500"
              initial={false}
              animate={prefersReducedMotion ? {} : { scale: [1, 1.5, 1] }}
              transition={{ duration: 3, repeat: Infinity }}
              aria-hidden="true"
            />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4]/10 to-[#8B31CD]/10 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500"
              aria-hidden="true"
            />
            <span className="relative z-10 text-base">See all projects</span>
            <motion.div
              className="relative"
              animate={prefersReducedMotion ? {} : { x: [0, 5, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <ArrowRight className="w-4 h-4" aria-hidden="true" />
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] to-[#8B31CD] blur-lg opacity-0 group-hover:opacity-50"
                animate={prefersReducedMotion ? {} : { scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                aria-hidden="true"
              />
            </motion.div>
          </ScrollLink>
        </motion.div>
      </div>
    </section>
  )
}