'use client'
import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, HelpCircle, Clock, Globe, Code, MessageCircle, type LucideProps } from 'lucide-react'

// FAQ item interface
interface FAQItem {
  question: string
  answer: string
  icon: React.ComponentType<LucideProps>
  color: string
}

// FAQ data with consistent styling - Updated for junior frontend developer
const faqData: FAQItem[] = [
  {
    question: "What's your experience level?",
    answer: "I'm a junior frontend developer with hands-on experience in React and TypeScript. I'm early in my career but passionate about creating quality websites with clean code.",
    icon: Code,
    color: '#00D9FF'
  },
  {
    question: "What technologies do you work with?",
    answer: "I work with HTML, CSS, JavaScript, React, TypeScript, TailwindCSS, and Next.js. I use Git/GitHub for code management and deploy on Vercel.",
    icon: Code,
    color: '#00D9FF'
  },
  {
    question: "What types of projects can you help with?",
    answer: "I build responsive websites, React apps, landing pages, and portfolio sites. I focus on clean designs that work great on all devices.",
    icon: Globe,
    color: '#00D9FF'
  },
  {
    question: "How do you approach new projects?",
    answer: "I start by understanding what you need, then create a clear plan. I keep you updated throughout and am always open to feedback.",
    icon: MessageCircle,
    color: '#00D9FF'
  },
  {
    question: "What's your typical project timeline?",
    answer: "Simple websites take 1-3 weeks, while React apps may take 4-8 weeks. I'll give you a realistic timeline based on your project needs.",
    icon: Clock,
    color: '#00D9FF'
  },
  {
    question: "Are you available for team collaboration?",
    answer: "Yes! I enjoy working with teams and learning from experienced developers. I use modern tools and follow good coding practices.",
    icon: HelpCircle,
    color: '#00D9FF'
  }
]

export function FAQSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(null)

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="relative bg-mono-bg py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32">
      <div className="max-w-4xl mx-auto px-8 max-[424px]:px-8 mobile:px-8 sm:px-8 md:px-8 lg:px-6 max-[424px]:max-w-4xl mobile:max-w-4xl"> {/* Match featured-projects section content width */}
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12 max-[424px]:mb-8 mobile:mb-8 sm:mb-10 md:mb-12 lg:mb-16"
        >
          <motion.p
            className="text-mono-secondary text-xs max-[424px]:text-xs mobile:text-xs md:text-xs lg:text-sm tracking-wide uppercase font-medium mb-3 max-[424px]:mb-2 mobile:mb-2 md:mb-3 lg:mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            FREQUENTLY ASKED
          </motion.p>

          <motion.h2
            className="text-3xl max-[424px]:text-3xl mobile:text-3xl md:text-4xl lg:text-5xl font-bold text-mono-text mb-4 max-[424px]:mb-3 mobile:mb-3 md:mb-5 lg:mb-6 leading-tight tracking-tight"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Common{' '}
            <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
              Questions
            </span>
          </motion.h2>

          <motion.p
            className="text-base max-[424px]:text-sm mobile:text-sm md:text-base lg:text-lg text-mono-secondary max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Get to know more about my skills and how I can help with your project
          </motion.p>
        </motion.div>

        {/* FAQ items */}
        <div className="space-y-3 max-[424px]:space-y-2 mobile:space-y-2 sm:space-y-3 md:space-y-3 lg:space-y-4">
          {faqData.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-mono-surface/[0.08] border border-mono-border rounded-xl max-[424px]:rounded-lg mobile:rounded-lg sm:rounded-xl md:rounded-xl lg:rounded-2xl backdrop-blur-xl overflow-hidden"
            >
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full p-4 max-[424px]:p-3 mobile:p-3 sm:p-4 md:p-5 lg:p-6 text-left hover:bg-mono-surface/[0.12] transition-all duration-300 focus:outline-none focus:bg-mono-surface/[0.12]"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 max-[424px]:gap-2.5 mobile:gap-2.5 sm:gap-3 md:gap-3 lg:gap-4">
                    <div
                      className="p-1.5 max-[424px]:p-1 mobile:p-1 sm:p-1.5 md:p-1.5 lg:p-2 rounded-md max-[424px]:rounded-sm mobile:rounded-sm sm:rounded-md md:rounded-md lg:rounded-lg flex-shrink-0"
                      style={{ backgroundColor: `${faq.color}20`, color: faq.color }}
                    >
                      <faq.icon className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-4 sm:h-4 md:w-4 md:h-4 lg:w-5 lg:h-5" />
                    </div>
                    <h3 className="text-mono-text font-semibold text-base max-[424px]:text-sm mobile:text-sm sm:text-base md:text-base lg:text-lg">
                      {faq.question}
                    </h3>
                  </div>
                  <motion.div
                    animate={{ rotate: openIndex === index ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                    className="flex-shrink-0"
                  >
                    <ChevronDown className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-4 sm:h-4 md:w-4 md:h-4 lg:w-5 lg:h-5 text-mono-secondary" />
                  </motion.div>
                </div>
              </button>

              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="px-4 pb-4 max-[424px]:px-3 max-[424px]:pb-3 mobile:px-3 mobile:pb-3 sm:px-4 sm:pb-4 md:px-5 md:pb-5 lg:px-6 lg:pb-6">
                      <div className="pl-8 max-[424px]:pl-6 mobile:pl-6 sm:pl-8 md:pl-10 lg:pl-12">
                        <p className="text-mono-secondary text-sm max-[424px]:text-xs mobile:text-xs sm:text-sm md:text-sm lg:text-base leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        {/* Additional help section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-12 max-[424px]:mt-8 mobile:mt-8 sm:mt-10 md:mt-12 lg:mt-16 text-center bg-mono-surface/[0.08] border border-mono-border rounded-xl max-[424px]:rounded-lg mobile:rounded-lg sm:rounded-xl md:rounded-xl lg:rounded-2xl p-6 max-[424px]:p-4 mobile:p-4 sm:p-5 md:p-6 lg:p-8 backdrop-blur-xl"
        >
          <div className="w-12 h-12 max-[424px]:w-10 max-[424px]:h-10 mobile:w-10 mobile:h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-accent-electric/20 rounded-full flex items-center justify-center mx-auto mb-3 max-[424px]:mb-2 mobile:mb-2 sm:mb-3 md:mb-3 lg:mb-4">
            <HelpCircle className="w-6 h-6 max-[424px]:w-5 max-[424px]:h-5 mobile:w-5 mobile:h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 text-accent-electric" />
          </div>

          <h3 className="text-lg max-[424px]:text-base mobile:text-base sm:text-lg md:text-lg lg:text-xl font-bold text-mono-text mb-2 max-[424px]:mb-1.5 mobile:mb-1.5 sm:mb-2.5 md:mb-2.5 lg:mb-3">
            Still have questions?
          </h3>

          <p className="text-sm max-[424px]:text-xs mobile:text-xs sm:text-sm md:text-sm lg:text-base text-mono-secondary mb-4 max-[424px]:mb-3 mobile:mb-3 sm:mb-4 md:mb-5 lg:mb-6 max-w-2xl mx-auto">
            I'd love to hear about your project! Feel free to reach out with any questions.
          </p>

          <div className="flex flex-col max-[424px]:flex-col mobile:flex-col sm:flex-row gap-3 max-[424px]:gap-2 mobile:gap-2 sm:gap-3 md:gap-3 lg:gap-4 justify-center items-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center justify-center gap-2 bg-accent-electric text-black font-semibold px-5 py-2.5 max-[424px]:px-4 max-[424px]:py-2 mobile:px-4 mobile:py-2 sm:px-5 sm:py-2.5 md:px-5 md:py-2.5 lg:px-6 lg:py-3 rounded-lg max-[424px]:rounded-md mobile:rounded-md sm:rounded-lg md:rounded-lg lg:rounded-xl hover:bg-blue-500 hover:shadow-lg hover:shadow-accent-electric/25 transition-all duration-300 text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm w-full sm:w-auto"
            >
              Send Email
            </a>
            <a
              href="https://wa.me/639123456789"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center gap-2 bg-mono-surface/30 border border-mono-border text-mono-text font-semibold px-5 py-2.5 max-[424px]:px-4 max-[424px]:py-2 mobile:px-4 mobile:py-2 sm:px-5 sm:py-2.5 md:px-5 md:py-2.5 lg:px-6 lg:py-3 rounded-lg max-[424px]:rounded-md mobile:rounded-md sm:rounded-lg md:rounded-lg lg:rounded-xl hover:bg-mono-surface/50 transition-all duration-300 text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm w-full sm:w-auto"
            >
              WhatsApp Chat
            </a>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
