import { HeroSection } from '@/components/sections/hero-section';
import { FeaturedProjectsSection } from '@/components/sections/home/<USER>';
import { TechStackSection } from '@/components/sections/home/<USER>';
import FlowingRibbon from '@/components/ui/flowing-ribbon';
// import { ProblemSolvingSection } from '@/components/sections/home/<USER>';
import { LearningGrowthSection } from '@/components/sections/home/<USER>';
import { PersonalStorySection } from '@/components/sections/home/<USER>';
// import { BlogPreviewSection } from '@/components/sections/home/<USER>';
import { ContactPreviewSection } from '@/components/sections/home/<USER>';
// import { TechnologyCloud } from '@/components/sections/home/<USER>';
import { SEOHead, seoConfigs } from '@/components/seo/seo-head';

export function HomePage() {
  return (
    <>
      <SEOHead {...seoConfigs.home} />
      <main className="min-h-screen bg-mono-bg">
        <HeroSection />
        <FeaturedProjectsSection />
        <TechStackSection />

        {/* Slanted flowing ribbon separator */}
        <FlowingRibbon />

        {/* <ProblemSolvingSection /> */}
        <LearningGrowthSection />
        <PersonalStorySection />
        {/* <BlogPreviewSection /> */}
        <ContactPreviewSection />
        {/* <TechnologyCloud /> */}
      </main>
    </>
  );
}