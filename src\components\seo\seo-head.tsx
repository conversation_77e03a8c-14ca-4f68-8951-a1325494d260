import { useEffect } from 'react'

interface SEOHeadProps {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: 'website' | 'article' | 'profile'
  author?: string
  publishedTime?: string
  modifiedTime?: string
}

export function SEOHead({
  title = '<PERSON><PERSON> - Frontend Developer | React & TypeScript Specialist',
  description = 'CJ <PERSON> - Frontend developer creating modern, accessible websites with React, TypeScript, and TailwindCSS. Based in Iligan, Northern Mindanao, Philippines.',
  keywords = 'frontend developer, react developer, typescript, tailwindcss, web development, philippines, portfolio, christian jerald jutba',
  image = 'https://cjjutba.site/hero-banner.png',
  url = 'https://cjjutba.site',
  type = 'website',
  author = 'Christian <PERSON>d <PERSON>',
  publishedTime,
  modifiedTime
}: SEOHeadProps) {
  
  useEffect(() => {
    // Update document title
    document.title = title
    
    // Update meta tags
    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`
      let meta = document.querySelector(selector) as HTMLMetaElement
      
      if (!meta) {
        meta = document.createElement('meta')
        if (property) {
          meta.setAttribute('property', name)
        } else {
          meta.setAttribute('name', name)
        }
        document.head.appendChild(meta)
      }
      
      meta.setAttribute('content', content)
    }
    
    // Basic meta tags
    updateMetaTag('description', description)
    updateMetaTag('keywords', keywords)
    updateMetaTag('author', author)
    updateMetaTag('robots', 'index, follow')
    
    // Open Graph tags
    updateMetaTag('og:title', title, true)
    updateMetaTag('og:description', description, true)
    updateMetaTag('og:image', image, true)
    updateMetaTag('og:url', url, true)
    updateMetaTag('og:type', type, true)
    updateMetaTag('og:site_name', 'CJ Jutba Portfolio', true)
    
    // Twitter Card tags
    updateMetaTag('twitter:card', 'summary_large_image', true)
    updateMetaTag('twitter:title', title, true)
    updateMetaTag('twitter:description', description, true)
    updateMetaTag('twitter:image', image, true)
    updateMetaTag('twitter:url', url, true)
    
    // Article specific tags
    if (type === 'article' && publishedTime) {
      updateMetaTag('article:published_time', publishedTime, true)
      updateMetaTag('article:author', author, true)
    }
    
    if (modifiedTime) {
      updateMetaTag('article:modified_time', modifiedTime, true)
    }
    
    // Update canonical link
    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement
    if (!canonical) {
      canonical = document.createElement('link')
      canonical.setAttribute('rel', 'canonical')
      document.head.appendChild(canonical)
    }
    canonical.setAttribute('href', url)
    
    // Add structured data for better SEO
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Christian Jerald Jutba",
      "alternateName": "CJ Jutba",
      "jobTitle": "Frontend Developer",
      "description": description,
      "url": url,
      "image": image,
      "sameAs": [
        "https://github.com/christianjeraldjutba",
        "https://linkedin.com/in/cjjutba"
      ],
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Iligan",
        "addressRegion": "Northern Mindanao",
        "addressCountry": "Philippines"
      },
      "knowsAbout": [
        "React",
        "TypeScript",
        "JavaScript",
        "TailwindCSS",
        "Frontend Development",
        "Web Development",
        "User Interface Design",
        "User Experience Design"
      ]
    }
    
    // Update or create structured data script
    let structuredDataScript = document.querySelector('script[type="application/ld+json"]')
    if (!structuredDataScript) {
      structuredDataScript = document.createElement('script')
      structuredDataScript.setAttribute('type', 'application/ld+json')
      document.head.appendChild(structuredDataScript)
    }
    structuredDataScript.textContent = JSON.stringify(structuredData)
    
  }, [title, description, keywords, image, url, type, author, publishedTime, modifiedTime])
  
  return null // This component doesn't render anything
}

// Page-specific SEO configurations
export const seoConfigs = {
  home: {
    title: 'CJ Jutba - Frontend Developer | React & TypeScript Specialist',
    description: 'Frontend developer creating modern, accessible websites with React, TypeScript, and TailwindCSS. View my portfolio and get in touch.',
    keywords: 'frontend developer, react developer, typescript, portfolio, web development, philippines',
    url: 'https://cjjutba.site'
  },
  about: {
    title: 'About CJ Jutba - Frontend Developer & React Specialist',
    description: 'Learn about CJ Jutba, a passionate frontend developer specializing in React, TypeScript, and modern web technologies. Based in Philippines.',
    keywords: 'about cj jutba, frontend developer story, react specialist, web developer philippines',
    url: 'https://cjjutba.site/about'
  },
  projects: {
    title: 'Projects - CJ Jutba Portfolio | React & TypeScript Applications',
    description: 'Explore CJ Jutba\'s portfolio of modern web applications built with React, TypeScript, and TailwindCSS. See live demos and source code.',
    keywords: 'react projects, typescript applications, web development portfolio, frontend projects',
    url: 'https://cjjutba.site/projects'
  },
  skills: {
    title: 'Skills & Technologies - CJ Jutba | Frontend Development Expertise',
    description: 'Discover CJ Jutba\'s technical skills and expertise in React, TypeScript, TailwindCSS, and modern frontend development technologies.',
    keywords: 'react skills, typescript expertise, frontend technologies, web development skills',
    url: 'https://cjjutba.site/skills'
  },
  contact: {
    title: 'Contact CJ Jutba - Frontend Developer | Get In Touch',
    description: 'Get in touch with CJ Jutba for frontend development projects, collaborations, or job opportunities. Based in Philippines.',
    keywords: 'contact cj jutba, hire frontend developer, react developer contact, web development services',
    url: 'https://cjjutba.site/contact'
  }
}
