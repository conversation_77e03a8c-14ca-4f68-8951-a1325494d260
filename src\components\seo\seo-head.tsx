import { useEffect } from 'react'

interface SEOHeadProps {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: 'website' | 'article' | 'profile'
  author?: string
  publishedTime?: string
  modifiedTime?: string
}

export function SEOHead({
  title = '<PERSON><PERSON> - Frontend Developer',
  description = '<PERSON><PERSON> - Frontend developer creating modern, accessible websites with React, TypeScript, and TailwindCSS. Based in Plaridel, Misamis Occidental, Philippines.',
  keywords = 'frontend developer, react developer, typescript, tailwindcss, web development, philippines, portfolio, christian jerald jutba',
  image = 'https://cjjutba.site/hero-banner.png',
  url = 'https://cjjutba.site',
  type = 'website',
  author = 'Christian <PERSON>d <PERSON>',
  publishedTime,
  modifiedTime
}: SEOHeadProps) {
  
  useEffect(() => {
    // Update document title
    document.title = title
    
    // Update meta tags
    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`
      let meta = document.querySelector(selector) as HTMLMetaElement
      
      if (!meta) {
        meta = document.createElement('meta')
        if (property) {
          meta.setAttribute('property', name)
        } else {
          meta.setAttribute('name', name)
        }
        document.head.appendChild(meta)
      }
      
      meta.setAttribute('content', content)
    }
    
    // Basic meta tags
    updateMetaTag('description', description)
    updateMetaTag('keywords', keywords)
    updateMetaTag('author', author)
    updateMetaTag('robots', 'index, follow')
    
    // Open Graph tags
    updateMetaTag('og:title', title, true)
    updateMetaTag('og:description', description, true)
    updateMetaTag('og:image', image, true)
    updateMetaTag('og:url', url, true)
    updateMetaTag('og:type', type, true)
    updateMetaTag('og:site_name', 'CJ Jutba Portfolio', true)
    
    // Twitter Card tags
    updateMetaTag('twitter:card', 'summary_large_image', true)
    updateMetaTag('twitter:title', title, true)
    updateMetaTag('twitter:description', description, true)
    updateMetaTag('twitter:image', image, true)
    updateMetaTag('twitter:url', url, true)
    
    // Article specific tags
    if (type === 'article' && publishedTime) {
      updateMetaTag('article:published_time', publishedTime, true)
      updateMetaTag('article:author', author, true)
    }
    
    if (modifiedTime) {
      updateMetaTag('article:modified_time', modifiedTime, true)
    }
    
    // Update canonical link
    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement
    if (!canonical) {
      canonical = document.createElement('link')
      canonical.setAttribute('rel', 'canonical')
      document.head.appendChild(canonical)
    }
    canonical.setAttribute('href', url)
    
    // Enhanced structured data for name ranking optimization
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Christian Jerald Jutba",
      "alternateName": ["CJ Jutba", "CJJUTBA", "cjjutba", "Christian Jutba", "Jerald Jutba"],
      "jobTitle": "Frontend Developer",
      "description": description,
      "url": url,
      "image": image,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": url
      },
      "sameAs": [
        "https://github.com/christianjeraldjutba",
        "https://linkedin.com/in/cjjutba",
        "https://cjjutba.site"
      ],
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Plaridel",
        "addressRegion": "Misamis Occidental",
        "addressCountry": "Philippines"
      },
      "knowsAbout": [
        "React",
        "TypeScript",
        "JavaScript",
        "TailwindCSS",
        "Frontend Development",
        "Web Development",
        "User Interface Design",
        "User Experience Design"
      ],
      "identifier": [
        {
          "@type": "PropertyValue",
          "name": "Portfolio Website",
          "value": "https://cjjutba.site"
        },
        {
          "@type": "PropertyValue",
          "name": "Professional Name",
          "value": "CJ Jutba"
        },
        {
          "@type": "PropertyValue",
          "name": "Full Name",
          "value": "Christian Jerald Jutba"
        }
      ]
    }
    
    // Update or create structured data script
    let structuredDataScript = document.querySelector('script[type="application/ld+json"]')
    if (!structuredDataScript) {
      structuredDataScript = document.createElement('script')
      structuredDataScript.setAttribute('type', 'application/ld+json')
      document.head.appendChild(structuredDataScript)
    }
    structuredDataScript.textContent = JSON.stringify(structuredData)
    
  }, [title, description, keywords, image, url, type, author, publishedTime, modifiedTime])
  
  return null // This component doesn't render anything
}

// Page-specific SEO configurations optimized for name-based ranking
export const seoConfigs = {
  home: {
    title: 'CJ Jutba - Christian Jerald Jutba | Frontend Developer Portfolio',
    description: 'CJ Jutba (Christian Jerald Jutba) - Professional frontend developer creating modern websites with React, TypeScript, and TailwindCSS. Official portfolio of CJJUTBA.',
    keywords: 'CJ Jutba, Christian Jerald Jutba, CJJUTBA, cjjutba, frontend developer, react developer, typescript, portfolio, web development, philippines, christian jutba, jerald jutba',
    url: 'https://cjjutba.site'
  },
  about: {
    title: 'About CJ Jutba - Christian Jerald Jutba | Frontend Developer Story',
    description: 'Learn about CJ Jutba (Christian Jerald Jutba), a passionate frontend developer from Philippines specializing in React, TypeScript, and modern web technologies.',
    keywords: 'about CJ Jutba, Christian Jerald Jutba story, CJJUTBA biography, frontend developer philippines, react specialist',
    url: 'https://cjjutba.site/about'
  },
  projects: {
    title: 'CJ Jutba Projects - Christian Jerald Jutba Portfolio | React Applications',
    description: 'Explore CJ Jutba\'s (Christian Jerald Jutba) portfolio of modern web applications built with React, TypeScript, and TailwindCSS. CJJUTBA project showcase.',
    keywords: 'CJ Jutba projects, Christian Jerald Jutba portfolio, CJJUTBA applications, react projects, typescript applications',
    url: 'https://cjjutba.site/projects'
  },
  skills: {
    title: 'CJ Jutba Skills - Christian Jerald Jutba | Frontend Development Expertise',
    description: 'Discover CJ Jutba\'s (Christian Jerald Jutba) technical skills and expertise in React, TypeScript, TailwindCSS, and modern frontend development.',
    keywords: 'CJ Jutba skills, Christian Jerald Jutba expertise, CJJUTBA technologies, react skills, typescript expertise',
    url: 'https://cjjutba.site/skills'
  },
  contact: {
    title: 'Contact CJ Jutba - Christian Jerald Jutba | Hire Frontend Developer',
    description: 'Get in touch with CJ Jutba (Christian Jerald Jutba) for frontend development projects, collaborations, or job opportunities. Contact CJJUTBA directly.',
    keywords: 'contact CJ Jutba, hire Christian Jerald Jutba, CJJUTBA contact, frontend developer philippines, react developer hire',
    url: 'https://cjjutba.site/contact'
  }
}
