import { Button } from '@/components/ui/button';
import { useCommandPalette } from '@/hooks/use-command-palette';
import { cn } from '@/lib/utils';
import { Command } from 'lucide-react';

interface NavActionsProps {
  isScrolled: boolean;
}

export function NavActions({ isScrolled: _isScrolled }: NavActionsProps) {
  const { open } = useCommandPalette();

  return (
    <div className="flex w-full flex-col space-y-3 sm:flex-row sm:gap-3 sm:space-y-0 md:w-fit">
      <Button
        variant="ghost"
        size="icon"
        className={cn(
          'bg-transparent text-mono-text-muted hover:text-mono-text hover:bg-white/5 transition-all duration-300 hover:scale-105 rounded-xl font-medium border border-transparent hover:border-white/10 hover:backdrop-blur-sm',
          'focus:outline-none focus:ring-2 focus:ring-white/20 focus:ring-offset-2 focus:ring-offset-black',
          'w-10 h-10 group relative hover:shadow-md'
        )}
        onClick={open}
        aria-label="Open command palette (⌘K)"
        title="Open command palette (⌘K)"
      >
        <Command className="w-4 h-4 transition-all duration-300 group-hover:text-white" />
      </Button>
    </div>
  );
}
