'use client'
import React from 'react'
import { motion, useScroll, useTransform } from 'framer-motion'

interface ScrollIndicatorProps {
    className?: string
    delay?: number
}

export function ScrollIndicator({ className = "", delay = 3 }: ScrollIndicatorProps) {
    const { scrollY } = useScroll()
    const [windowHeight, setWindowHeight] = React.useState(0)

    // Get window height safely
    React.useEffect(() => {
        setWindowHeight(window.innerHeight)
        const handleResize = () => setWindowHeight(window.innerHeight)
        window.addEventListener('resize', handleResize)
        return () => window.removeEventListener('resize', handleResize)
    }, [])

    // Hide the indicator when user scrolls down from the hero section
    // Using viewport height as reference - hide after scrolling past 70% of viewport
    const hideThreshold = windowHeight * 0.7
    const opacity = useTransform(scrollY, [0, hideThreshold], [1, 0])
    const scrollY_transform = useTransform(scrollY, [0, hideThreshold], [0, 30])

    // Scroll to featured projects section
    const scrollToFeaturedProjects = () => {
        const featuredProjectsElement = document.getElementById('featured-projects')
        if (featuredProjectsElement) {
            featuredProjectsElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            })
        }
    }

    return (
        <motion.div
            className={`flex flex-col items-center justify-center cursor-pointer ${className}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay, duration: 1 }}
            style={{ opacity, y: scrollY_transform }}
            onClick={scrollToFeaturedProjects}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
        >
            {/* Text with subtle floating animation */}
            <motion.span 
                className="text-xs font-medium tracking-wider uppercase text-center text-mono-secondary hover:text-mono-accent transition-colors duration-300 mb-2"
                animate={{
                    y: [-2, 2, -2],
                }}
                transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                }}
            >
                Scroll to explore
            </motion.span>
            
            {/* Scroll indicator container - perfectly centered and straight */}
            <div className="flex items-center justify-center">
                <motion.div
                    className="w-6 h-10 border-2 border-mono-accent/30 rounded-full flex items-center justify-center hover:border-mono-accent hover:bg-mono-accent/5 transition-all duration-300 relative"
                    whileHover={{ borderColor: 'rgb(170, 170, 170)' }}
                >
                    {/* Animated dot inside - moves straight up and down */}
                    <motion.div
                        className="w-1 h-3 bg-mono-accent rounded-full absolute"
                        animate={{
                            y: [-8, 8, -8],
                            opacity: [1, 0.3, 1],
                        }}
                        transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut",
                        }}
                        style={{
                            left: '50%',
                            transform: 'translateX(-50%)',
                        }}
                    />
                </motion.div>
            </div>
        </motion.div>
    )
}