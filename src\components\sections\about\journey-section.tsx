import React, { useRef } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';
import { BookOpen, Code, Laptop, Building2, Trophy } from 'lucide-react';
import { sectionConfigs } from '@/styles/spacing';

interface JourneyStep {
  year: string;
  title: string;
  description: string;
  icon: React.ElementType;
  color: string;
}

const journeySteps: JourneyStep[] = [
  {
    year: '2021',
    title: 'Started Learning',
    description: 'Started studying Computer Engineering during COVID. Had no laptop and slow internet, but I worked hard and passed all my classes.',
    icon: BookOpen,
    color: '#00FF85' // Neon green
  },
  {
    year: '2022',
    title: 'Found My Interest',
    description: 'Moved to the city for school. Used school computers to learn HTML, CSS, and JavaScript. Built my first website and loved making things look good on the web.',
    icon: Code,
    color: '#1E90FF' // Electric blue
  },
  {
    year: '2023',
    title: 'Got My Own Laptop',
    description: 'Finally got my own laptop! This changed everything. I could practice coding anytime. Spent many nights learning and building small projects.',
    icon: Laptop,
    color: '#FF0099' // Vivid pink
  },
  {
    year: '2024',
    title: 'First Work Experience',
    description: 'Did my internship at a hospital IT team. Worked with real websites and learned how technology helps people. Started building my own projects.',
    icon: Building2,
    color: '#8B31CD' // Purple
  },
  {
    year: '2025',
    title: 'Ready to Work',
    description: 'Graduated with my degree! Now I focus on building websites. I love making sites that look good and work well for everyone.',
    icon: Trophy,
    color: '#00FFFF' // Cyan
  }
];

const JourneyCard: React.FC<{ step: JourneyStep; index: number }> = ({ step, index }) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = React.useState(false);

  const { scrollYProgress } = useScroll({
    target: cardRef,
    offset: ["start end", "end start"]
  });

  // Smoother scroll animations with refined spring settings
  const opacity = useSpring(
    useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]),
    { stiffness: 80, damping: 18, restDelta: 0.0005 }
  );

  const x = useSpring(
    useTransform(
      scrollYProgress,
      [0, 0.2],
      [index % 2 === 0 ? -20 : 20, 0] // Reduced movement for subtlety
    ),
    { stiffness: 80, damping: 18, restDelta: 0.0005 }
  );

  const y = useSpring(
    useTransform(scrollYProgress, [0, 1], [15, -15]), // Reduced movement
    { stiffness: 80, damping: 18, restDelta: 0.0005 }
  );

  const Icon = step.icon;

  return (
    <motion.div
      ref={cardRef}
      style={{
        opacity,
        x,
        y,
        willChange: 'transform, opacity'
      }}
      className="relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className={`relative flex ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'} items-center gap-8`}>
        {/* Timeline dot and line */}
        <div className="hidden md:flex absolute left-1/2 transform -translate-x-1/2 top-8 flex-col items-center z-20">
          {/* Timeline line */}
          <div className="w-px h-32 bg-gradient-to-b from-mono-border/40 to-mono-border/10" />

          {/* Timeline dot */}
          <motion.div
            className="w-4 h-4 rounded-full border-2 border-mono-border bg-mono-bg relative"
            style={{
              borderColor: step.color,
              boxShadow: `0 0 20px ${step.color}40`
            }}
            animate={{
              scale: isHovered ? 1.2 : 1, // Reduced scale for subtlety
              boxShadow: isHovered
                ? `0 0 25px ${step.color}50, 0 0 40px ${step.color}25` // Softer glow
                : `0 0 20px ${step.color}40`
            }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          >
            <motion.div
              className="absolute inset-1 rounded-full"
              style={{ backgroundColor: step.color }}
              animate={{
                opacity: isHovered ? 0.7 : 0.6,
                scale: isHovered ? 1.1 : 1 // Reduced scale
              }}
              transition={{ duration: 0.2, ease: "easeOut" }}
            />
          </motion.div>
        </div>

        {/* Content card */}
        <div className="w-full md:w-[calc(50%-2rem)]">
          <motion.div
            className="relative bg-mono-surface-dark/5 backdrop-blur-xl border border-mono-border/30 rounded-3xl p-6 max-[424px]:p-5 mobile:p-5 sm:p-6 md:p-6 lg:p-10 shadow-2xl"
            initial={{ scale: 1, y: 0 }}
            animate={{
              scale: isHovered ? 1.02 : 1,
              y: isHovered ? -5 : 0,
            }}
            transition={{
              type: "spring",
              stiffness: 100,
              damping: 20,
              mass: 0.8
            }}
            whileHover={{
              borderColor: step.color + '50',
              boxShadow: `0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px ${step.color}20`,
              transition: { duration: 0.3 }
            }}
          >
            {/* Enhanced year badge */}
            <motion.div
              className="absolute -top-6 max-[424px]:-top-5 mobile:-top-5 sm:-top-6 md:-top-6 lg:-top-8 left-6 max-[424px]:left-5 mobile:left-5 sm:left-6 md:left-6 lg:left-8 px-4 max-[424px]:px-3 mobile:px-3 sm:px-4 md:px-4 lg:px-6 py-2 max-[424px]:py-1.5 mobile:py-1.5 sm:py-2 md:py-2 lg:py-3 rounded-xl max-[424px]:rounded-lg mobile:rounded-lg sm:rounded-xl md:rounded-xl lg:rounded-2xl text-sm max-[424px]:text-xs mobile:text-xs sm:text-sm md:text-sm lg:text-base font-bold backdrop-blur-xl"
              style={{
                background: 'linear-gradient(135deg, rgba(26, 26, 26, 0.9) 0%, rgba(15, 15, 15, 0.95) 100%)',
                border: `2px solid ${step.color}`,
                boxShadow: `0 8px 32px ${step.color}25, 0 0 0 1px ${step.color}10`,
                zIndex: 100
              }}
              initial={{ scale: 1, y: 0 }}
              animate={{
                scale: isHovered ? 1.05 : 1,
                y: isHovered ? -2 : 0,
                boxShadow: isHovered
                  ? `0 12px 40px ${step.color}35, 0 0 0 1px ${step.color}20`
                  : `0 8px 32px ${step.color}25, 0 0 0 1px ${step.color}10`,
              }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              <span
                className="text-white font-bold tracking-wide"
                style={{
                  textShadow: '0 2px 4px rgba(0,0,0,0.6)'
                }}
              >
                {step.year}
              </span>
            </motion.div>

            {/* Enhanced icon with premium styling */}
            <div className="mb-6 max-[424px]:mb-4 mobile:mb-4 sm:mb-5 md:mb-6 lg:mb-8 mt-6 max-[424px]:mt-4 mobile:mt-4 sm:mt-5 md:mt-6 lg:mt-8 flex justify-center">
              <motion.div
                className="relative inline-flex items-center justify-center w-16 max-[424px]:w-14 mobile:w-14 sm:w-16 md:w-16 lg:w-20 h-16 max-[424px]:h-14 mobile:h-14 sm:h-16 md:h-16 lg:h-20 rounded-xl max-[424px]:rounded-lg mobile:rounded-lg sm:rounded-xl md:rounded-xl lg:rounded-2xl backdrop-blur-sm"
                style={{
                  background: `linear-gradient(135deg, ${step.color}10 0%, ${step.color}05 100%)`,
                  border: `1px solid ${step.color}20`
                }}
                initial={{ rotate: 0, scale: 1 }}
                animate={{
                  rotate: isHovered ? 5 : 0,
                  scale: isHovered ? 1.08 : 1,
                  background: isHovered
                    ? `linear-gradient(135deg, ${step.color}15 0%, ${step.color}08 100%)`
                    : `linear-gradient(135deg, ${step.color}10 0%, ${step.color}05 100%)`,
                  borderColor: isHovered ? step.color + '30' : step.color + '20'
                }}
                transition={{
                  duration: 0.4,
                  ease: "easeOut",
                  type: "spring",
                  stiffness: 100,
                  damping: 20
                }}
              >
                <Icon
                  className="w-8 max-[424px]:w-7 mobile:w-7 sm:w-8 md:w-8 lg:w-10 h-8 max-[424px]:h-7 mobile:h-7 sm:h-8 md:h-8 lg:h-10 relative z-10"
                  style={{
                    color: step.color,
                    filter: `drop-shadow(0 0 12px ${step.color}50)`
                  }}
                />
                {/* Enhanced icon glow effect */}
                <motion.div
                  className="absolute w-16 h-16 blur-2xl -z-10 rounded-full"
                  style={{ backgroundColor: step.color }}
                  initial={{ opacity: 0.1, scale: 1 }}
                  animate={{
                    opacity: isHovered ? 0.2 : 0.1,
                    scale: isHovered ? 1.2 : 1
                  }}
                  transition={{ duration: 0.4, ease: "easeOut" }}
                />
              </motion.div>
            </div>

            {/* Enhanced content with premium typography */}
            <motion.div
              className="space-y-4 max-[424px]:space-y-3 mobile:space-y-3 sm:space-y-4 md:space-y-4 lg:space-y-6"
              initial={{ opacity: 0.95, y: 2 }}
              animate={{
                opacity: isHovered ? 1 : 0.95,
                y: isHovered ? 0 : 2
              }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              <motion.h3
                className="text-2xl max-[424px]:text-xl mobile:text-xl sm:text-2xl md:text-2xl lg:text-3xl font-bold text-mono-text mb-3 max-[424px]:mb-2 mobile:mb-2 sm:mb-3 md:mb-3 lg:mb-4 tracking-tight leading-tight"
                initial={{ scale: 1 }}
                animate={{
                  scale: isHovered ? 1.01 : 1
                }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              >
                {step.title}
              </motion.h3>

              {/* Enhanced description with better typography */}
              <motion.p
                className="text-mono-text/80 leading-relaxed tracking-wide font-light text-base max-[424px]:text-sm mobile:text-sm sm:text-base md:text-base lg:text-lg max-w-md max-[424px]:max-w-xs mobile:max-w-xs sm:max-w-sm"
                initial={{ opacity: 0.85 }}
                animate={{
                  opacity: isHovered ? 1 : 0.85
                }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              >
                {step.description}
              </motion.p>
            </motion.div>

            {/* Enhanced hover glow effect */}
            <motion.div
              className="absolute inset-0 rounded-3xl"
              style={{
                background: `radial-gradient(circle at center, ${step.color}08 0%, transparent 70%)`
              }}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{
                opacity: isHovered ? 1 : 0,
                scale: isHovered ? 1 : 0.95
              }}
              transition={{ duration: 0.4, ease: "easeOut" }}
            />
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export function JourneySection() {
  const containerRef = useRef<HTMLDivElement>(null);

  // Refined scroll-based animations for background
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start 0.95", "end 0.05"] // Adjusted for smoother transition
  });

  const y1 = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, -15]), // Reduced movement
    { stiffness: 80, damping: 20, restDelta: 0.0005 } // Smoother spring
  );

  const y2 = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, 15]), // Reduced movement
    { stiffness: 80, damping: 20, restDelta: 0.0005 } // Smoother spring
  );

  return (
    <section ref={containerRef} className="relative bg-mono-bg py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32 overflow-hidden">
      {/* Enhanced premium background effects */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at 30% 20%, rgba(207,111,244,0.02) 0%, transparent 50%), radial-gradient(circle at 70% 80%, rgba(139,49,205,0.015) 0%, transparent 50%)',
          }}
          animate={{
            scale: [1, 1.05, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />

        <motion.div
          className="absolute top-1/4 left-1/4 w-[800px] h-[800px] bg-mono-surface/6 rounded-full blur-[150px]"
          style={{
            y: y1,
            willChange: 'transform'
          }}
          initial={{ opacity: 0.05, scale: 1 }}
          animate={{
            opacity: [0.05, 0.08, 0.05],
            scale: [1, 1.02, 1],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-[1000px] h-[1000px] bg-mono-surface-light/6 rounded-full blur-[180px]"
          style={{
            y: y2,
            willChange: 'transform'
          }}
          initial={{ opacity: 0.03, scale: 1.05 }}
          animate={{
            opacity: [0.03, 0.06, 0.03],
            scale: [1.05, 1.01, 1.05],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />
      </div>

      {/* Enhanced premium section header */}
      <div className={`relative z-10 ${sectionConfigs.journeySection.headerContainer} lg:max-w-4xl`}>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="space-y-4 max-[424px]:space-y-3 mobile:space-y-3 sm:space-y-3 md:space-y-3 text-center"
        >
          <p className="text-mono-accent/80 text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs font-medium tracking-[0.25em] uppercase">
            MY JOURNEY
          </p>
          <h2 className="text-4xl max-[424px]:text-3xl mobile:text-3xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight tracking-tight">
            My{' '}
            <span className="relative inline-block">
              <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                Growth
              </span>
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-20"
                initial={{ opacity: 0.2 }}
                animate={{
                  opacity: [0.2, 0.3, 0.2],
                  scale: [1, 1.05, 1]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
              />
            </span>
          </h2>
        </motion.div>
      </div>

      <div className={`relative z-10 ${sectionConfigs.journeySection.timelineContainer} lg:max-w-4xl`}>
        {/* Enhanced journey timeline */}
        <div className="relative">
          {/* Enhanced central timeline line for desktop */}
          <div className="hidden md:block absolute left-1/2 transform -translate-x-1/2 top-0 bottom-0 w-px">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-mono-border/40 to-transparent" />
            <motion.div
              className="absolute inset-0 bg-gradient-to-b from-transparent via-[#CF6FF4]/30 to-transparent"
              animate={{
                opacity: [0.3, 0.6, 0.3],
                scaleY: [0.8, 1, 0.8]
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut"
              }}
            />
          </div>

          {/* Timeline cards with enhanced spacing */}
          <div className={sectionConfigs.journeySection.cardSpacing}>
            {journeySteps.map((step, index) => (
              <JourneyCard key={step.year} step={step} index={index} />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
