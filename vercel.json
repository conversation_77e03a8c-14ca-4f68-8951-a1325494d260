{"rewrites": [{"source": "/((?!api/).*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/sitemap.xml", "headers": [{"key": "Content-Type", "value": "application/xml"}]}, {"source": "/robots.txt", "headers": [{"key": "Content-Type", "value": "text/plain"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|webp|ico|pdf))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}