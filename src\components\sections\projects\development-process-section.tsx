import React from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Palette, Code2, Smartphone, Rocket } from 'lucide-react';

const processes = [
  {
    icon: Palette,
    title: 'Design & Planning',
    description: 'Transforming ideas into pixel-perfect designs with user experience at the forefront of every decision.',
    details: [
      'UI/UX wireframing',
      'Component architecture',
      'Responsive design planning'
    ],
    cardStyle: {
      background: 'from-[#2A0A1A] to-[#180712]',
      glow: 'from-[#FF0099]/10 to-transparent'
    },
    iconBg: 'bg-[#FF0099]/10',
    iconColor: 'text-[#FF0099]',
    accentColor: '#FF0099'
  },
  {
    icon: Code2,
    title: 'Frontend Development',
    description: 'Building interactive, performant web applications using modern React ecosystem and cutting-edge technologies.',
    details: [
      'React component development',
      'TypeScript implementation',
      'Performance optimization'
    ],
    cardStyle: {
      background: 'from-[#0A1A2A] to-[#071218]',
      glow: 'from-[#1E90FF]/10 to-transparent'
    },
    iconBg: 'bg-[#1E90FF]/10',
    iconColor: 'text-[#1E90FF]',
    accentColor: '#1E90FF'
  },
  {
    icon: Smartphone,
    title: 'Responsive & Testing',
    description: 'Ensuring seamless experiences across all devices with comprehensive testing and accessibility standards.',
    details: [
      'Mobile-first approach',
      'Cross-browser testing',
      'Accessibility compliance'
    ],
    cardStyle: {
      background: 'from-[#0A2A1C] to-[#071812]',
      glow: 'from-[#00FF85]/10 to-transparent'
    },
    iconBg: 'bg-[#00FF85]/10',
    iconColor: 'text-[#00FF85]',
    accentColor: '#00FF85'
  },
  {
    icon: Rocket,
    title: 'Deploy & Optimize',
    description: 'Launching projects with modern deployment strategies and continuous performance monitoring.',
    details: [
      'Vercel deployment',
      'Performance monitoring',
      'SEO optimization'
    ],
    cardStyle: {
      background: 'from-[#2A1A0A] to-[#1A1005]',
      glow: 'from-[#FFA500]/10 to-transparent'
    },
    iconBg: 'bg-[#FFA500]/10',
    iconColor: 'text-[#FFA500]',
    accentColor: '#FFA500'
  }
];

export function DevelopmentProcessSection() {
  const { scrollYProgress } = useScroll();
  const y = useTransform(scrollYProgress, [0, 1], [0, -50]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [1, 1, 1, 0.8]);

  return (
    <section className="relative bg-mono-bg overflow-hidden py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32">
      {/* Enhanced background with parallax */}
      <motion.div
        className="absolute inset-0"
        style={{ y, opacity }}
        aria-hidden="true"
      >
        <motion.div
          className="absolute top-1/4 left-1/3 w-[400px] h-[400px] bg-mono-accent/5 rounded-full blur-[80px] will-change-transform"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/3 w-[500px] h-[500px] bg-purple-500/5 rounded-full blur-[100px] will-change-transform"
          animate={{
            scale: [1.1, 1, 1.1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </motion.div>

      <div className="relative z-10 max-w-4xl mx-auto px-8 max-[424px]:px-8 mobile:px-8 sm:px-8 md:px-8 lg:px-6 max-[424px]:max-w-4xl mobile:max-w-4xl"> {/* Match featured-projects section content width */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mb-12 max-[424px]:mb-8 mobile:mb-8 sm:mb-10 md:mb-12 lg:mb-16"
        >
          <motion.h2
            className="text-4xl max-[424px]:text-3xl mobile:text-3xl sm:text-4xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight tracking-tight mb-4 max-[424px]:mb-3 mobile:mb-3 md:mb-4 lg:mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Frontend{' '}
            <span className="relative inline-block">
              <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                Workflow
              </span>
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-15"
                initial={{ opacity: 0.15 }}
                animate={{
                  opacity: [0.15, 0.25, 0.15],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
                aria-hidden="true"
              />
            </span>
          </motion.h2>
          <motion.p
            className="text-mono-secondary text-base max-[424px]:text-sm mobile:text-sm sm:text-base md:text-base lg:text-lg max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            My systematic approach to crafting exceptional frontend experiences that combine beautiful design with seamless functionality
          </motion.p>
        </motion.div>

        {/* Enhanced Process Grid */}
        <div className="grid max-[424px]:grid-cols-1 mobile:grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-6 max-[424px]:gap-4 mobile:gap-4 sm:gap-5 md:gap-6 lg:gap-8">
          {processes.map((process, index) => (
            <motion.div
              key={process.title}
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                duration: 0.6,
                delay: index * 0.15,
                ease: "easeOut"
              }}
              viewport={{ once: true }}
              whileHover={{
                y: -8,
                transition: { duration: 0.3, ease: "easeOut" }
              }}
              className={`group relative bg-gradient-to-br ${process.cardStyle.background} border border-mono-border rounded-2xl max-[424px]:rounded-xl mobile:rounded-xl sm:rounded-2xl md:rounded-2xl lg:rounded-3xl p-6 max-[424px]:p-4 mobile:p-4 sm:p-5 md:p-6 lg:p-8 backdrop-blur-xl hover:border-opacity-60 transition-all duration-500 transform-gpu will-change-transform overflow-hidden cursor-pointer`}
            >
              {/* Background glow effect */}
              <div className={`absolute inset-0 bg-gradient-to-br ${process.cardStyle.glow} opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />

              {/* Subtle gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              {/* Content */}
              <div className="relative z-10">
                {/* Enhanced Icon */}
                <motion.div
                  className={`w-14 h-14 max-[424px]:w-10 max-[424px]:h-10 mobile:w-10 mobile:h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-xl max-[424px]:rounded-lg mobile:rounded-lg sm:rounded-xl md:rounded-xl lg:rounded-2xl ${process.iconBg} flex items-center justify-center mb-4 max-[424px]:mb-3 mobile:mb-3 sm:mb-4 md:mb-5 lg:mb-6 group-hover:scale-110 transition-transform duration-300`}
                  whileHover={{ rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <process.icon className={`w-7 h-7 max-[424px]:w-5 max-[424px]:h-5 mobile:w-5 mobile:h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 ${process.iconColor}`} />
                </motion.div>

                <div className="space-y-3 max-[424px]:space-y-2 mobile:space-y-2 sm:space-y-3 md:space-y-3 lg:space-y-4">
                  {/* Title with micro-interaction */}
                  <motion.h3
                    className="text-xl max-[424px]:text-lg mobile:text-lg sm:text-lg md:text-xl lg:text-2xl font-bold text-mono-text group-hover:text-white transition-colors duration-300"
                    whileHover={{ x: 2 }}
                    transition={{ duration: 0.2 }}
                  >
                    {process.title}
                  </motion.h3>

                  {/* Description */}
                  <p className="text-mono-secondary text-sm max-[424px]:text-xs mobile:text-xs sm:text-sm md:text-sm lg:text-base leading-relaxed group-hover:text-mono-secondary transition-colors duration-300">
                    {process.description}
                  </p>

                  {/* Enhanced Details List */}
                  <ul className="space-y-2 max-[424px]:space-y-1.5 mobile:space-y-1.5 sm:space-y-2 md:space-y-2.5 lg:space-y-3 mt-4 max-[424px]:mt-3 mobile:mt-3 sm:mt-4 md:mt-5 lg:mt-6">
                    {process.details.map((detail, idx) => (
                      <motion.li
                        key={idx}
                        className="flex items-center gap-2.5 max-[424px]:gap-2 mobile:gap-2 sm:gap-2.5 md:gap-2.5 lg:gap-3 text-mono-secondary text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm group-hover:text-mono-secondary transition-colors duration-300"
                        initial={{ opacity: 0, x: -10 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{
                          delay: (index * 0.15) + (idx * 0.1),
                          duration: 0.4
                        }}
                        viewport={{ once: true }}
                        whileHover={{ x: 4 }}
                      >
                        <motion.div
                          className="w-1.5 h-1.5 max-[424px]:w-1 max-[424px]:h-1 mobile:w-1 mobile:h-1 sm:w-1.5 sm:h-1.5 md:w-1.5 md:h-1.5 lg:w-2 lg:h-2 rounded-full flex-shrink-0"
                          style={{ backgroundColor: process.accentColor }}
                          whileHover={{ scale: 1.2 }}
                          transition={{ duration: 0.2 }}
                        />
                        <span className="leading-relaxed">{detail}</span>
                      </motion.li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Subtle animated border */}
              <motion.div
                className="absolute inset-0 rounded-3xl border opacity-0 group-hover:opacity-100"
                style={{ borderColor: `${process.accentColor}20` }}
                initial={false}
                animate={{
                  opacity: 0,
                  scale: 1,
                }}
                whileHover={{
                  opacity: [0, 0.5, 0],
                  scale: [1, 1.01, 1],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            </motion.div>
          ))}
        </div>

        {/* Process Flow Indicator */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 max-[424px]:mt-10 sm:mt-10 md:mt-12 lg:mt-16 text-center"
        >
          <div className="flex items-center justify-center gap-4 max-[424px]:gap-3 sm:gap-3 md:gap-3 lg:gap-4 max-w-md mx-auto">
            {processes.map((process, index) => (
              <React.Fragment key={index}>
                <motion.div
                  className="w-3 h-3 max-[424px]:w-2.5 max-[424px]:h-2.5 sm:w-2.5 sm:h-2.5 md:w-2.5 md:h-2.5 lg:w-3 lg:h-3 rounded-full"
                  style={{ backgroundColor: `${process.accentColor}60` }}
                  whileHover={{
                    scale: 1.3,
                    backgroundColor: process.accentColor
                  }}
                  transition={{ duration: 0.2 }}
                />
                {index < processes.length - 1 && (
                  <motion.div
                    className="flex-1 h-px bg-gradient-to-r from-mono-accent/40 to-mono-accent/20"
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    transition={{ duration: 0.8, delay: 0.8 + (index * 0.2) }}
                    viewport={{ once: true }}
                  />
                )}
              </React.Fragment>
            ))}
          </div>
          <motion.p
            className="text-mono-secondary text-sm max-[424px]:text-xs sm:text-xs md:text-xs lg:text-sm mt-4 max-[424px]:mt-3 sm:mt-3 md:mt-3 lg:mt-4 font-medium"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.2 }}
            viewport={{ once: true }}
          >
            From concept to deployment
          </motion.p>
        </motion.div>
      </div>
    </section>
  );
}