'use client'
import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, Clock, ArrowRight, ExternalLink, Eye } from 'lucide-react'
import { Button } from '@/components/ui/button'

// Article data interface
interface Article {
  title: string
  excerpt: string
  content: string
  date: string
  readTime: string
  category: string
  tags: string[]
  image: string
  featured: boolean
  accentColor: string
  cardStyle: {
    background: string
    glow: string
  }
}

// Featured articles data with consistent styling
const featuredArticles: Article[] = [
  {
    title: 'Building Scalable React Applications with TypeScript',
    excerpt: 'Learn how to structure large React applications using TypeScript, best practices for component architecture, and advanced patterns for maintainable code.',
    content: 'In this comprehensive guide, we explore the best practices for building scalable React applications with TypeScript...',
    date: '2024-01-15',
    readTime: '8 min read',
    category: 'React',
    tags: ['React', 'TypeScript', 'Architecture', 'Best Practices'],
    image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
    featured: true,
    accentColor: '#00FF85',
    cardStyle: {
      background: 'from-[#0A2A1C] to-[#071812]',
      glow: 'from-[#00FF85]/10 to-transparent'
    }
  },
  {
    title: 'Node.js Performance Optimization Strategies',
    excerpt: 'Discover proven techniques to optimize Node.js applications for better performance, including memory management, async patterns, and monitoring.',
    content: 'Performance is crucial for Node.js applications. This post covers various optimization strategies...',
    date: '2024-01-05',
    readTime: '10 min read',
    category: 'Node.js',
    tags: ['Node.js', 'Performance', 'Backend', 'Optimization'],
    image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
    featured: true,
    accentColor: '#1E90FF',
    cardStyle: {
      background: 'from-[#0A1A2A] to-[#071218]',
      glow: 'from-[#1E90FF]/10 to-transparent'
    }
  },
  {
    title: 'Modern CSS Techniques for Better User Interfaces',
    excerpt: 'Explore the latest CSS features including Grid, Flexbox, Custom Properties, and Container Queries to create responsive and beautiful user interfaces.',
    content: 'CSS has evolved significantly in recent years. This article covers modern CSS techniques...',
    date: '2024-01-10',
    readTime: '6 min read',
    category: 'CSS',
    tags: ['CSS', 'UI/UX', 'Frontend', 'Design'],
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
    featured: true,
    accentColor: '#FF0099',
    cardStyle: {
      background: 'from-[#2A0A1A] to-[#180712]',
      glow: 'from-[#FF0099]/10 to-transparent'
    }
  }
]

// Animation variants
const transitionVariants = {
  item: {
    hidden: {
      opacity: 0,
      filter: 'blur(12px)',
      y: 12,
    },
    visible: {
      opacity: 1,
      filter: 'blur(0px)',
      y: 0,
      transition: {
        type: 'spring' as const,
        bounce: 0.3,
        duration: 1.5,
      },
    },
  },
}

export function FeaturedArticlesSection() {
  return (
    <section id="featured" className="relative bg-mono-bg py-24 lg:py-32">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section header */}
        <motion.div
          variants={transitionVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.p 
            className="text-mono-secondary text-sm tracking-wide uppercase font-medium mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            FEATURED CONTENT
          </motion.p>
          
          <motion.h2 
            className="text-3xl md:text-5xl font-bold text-mono-text mb-6 leading-tight tracking-tight"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Latest{' '}
            <span className="italic font-cursive text-transparent bg-clip-text bg-gradient-to-r from-accent-electric to-accent-vivid">
              Insights
            </span>
          </motion.h2>
          
          <motion.p 
            className="text-lg text-mono-secondary max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Deep dives into modern web development, practical tutorials, and insights 
            from real-world projects that help developers build better applications
          </motion.p>
        </motion.div>

        {/* Featured articles grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {featuredArticles.map((article, index) => (
            <motion.article
              key={article.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group cursor-pointer"
            >
              <div className={`relative bg-gradient-to-br ${article.cardStyle.background} border border-mono-border rounded-3xl overflow-hidden backdrop-blur-xl hover:border-opacity-60 transition-all duration-500`}>
                {/* Background glow effect */}
                <div className={`absolute inset-0 bg-gradient-to-br ${article.cardStyle.glow} opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />
                
                {/* Article image */}
                <div className="relative overflow-hidden">
                  <img
                    src={article.image}
                    alt={article.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4">
                    <span 
                      className="px-3 py-1 text-black text-sm font-medium rounded-full"
                      style={{ backgroundColor: article.accentColor }}
                    >
                      Featured
                    </span>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                </div>

                {/* Article content */}
                <div className="relative p-6 space-y-4">
                  {/* Meta information */}
                  <div className="flex items-center gap-4 text-mono-secondary text-sm">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {new Date(article.date).toLocaleDateString()}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {article.readTime}
                    </div>
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-bold text-mono-text group-hover:text-opacity-90 transition-colors duration-300 leading-tight">
                    {article.title}
                  </h3>

                  {/* Excerpt */}
                  <p className="text-mono-secondary leading-relaxed line-clamp-3">
                    {article.excerpt}
                  </p>

                  {/* Footer */}
                  <div className="flex items-center justify-between pt-2">
                    <span 
                      className="px-3 py-1 bg-mono-surface/30 border border-mono-border/50 rounded-full text-sm font-medium"
                      style={{ color: article.accentColor }}
                    >
                      {article.category}
                    </span>
                    <ArrowRight 
                      className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300"
                      style={{ color: article.accentColor }}
                    />
                  </div>
                </div>
              </div>
            </motion.article>
          ))}
        </div>

        {/* View all articles CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Button 
            className="bg-mono-surface border border-mono-border hover:bg-mono-surface-light transition-all duration-300 px-8 py-3 h-auto"
          >
            View All Articles
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
