/**
 * Unified Spacing System
 * Centralized configuration for consistent spacing and container widths across all sections
 */

// Container width configurations
export const containerWidths = {
  // Main container widths for different section types
  hero: 'max-w-6xl',
  content: 'max-w-4xl',
  narrow: 'max-w-3xl',
  wide: 'max-w-7xl',
  
  // Mobile width constraints (for ≤424px and 425px-639px breakpoints)
  mobile: {
    small: 'max-[424px]:max-w-[95%]',      // ≤424px
    standard: 'mobile:max-w-[95%]',        // 425px-639px
  }
} as const;

// Padding configurations
export const containerPadding = {
  // Standard padding for different breakpoints
  desktop: 'md:px-8',
  tablet: 'px-4',
  
  // Mobile padding (for ≤424px and 425px-639px breakpoints)
  mobile: {
    small: 'max-[424px]:px-2',             // ≤424px
    standard: 'mobile:px-2',               // 425px-639px
  }
} as const;

// Content-specific padding (for inner content elements)
export const contentPadding = {
  // Text content padding
  text: {
    desktop: 'lg:px-0',
    tablet: 'md:px-4',
    mobile: {
      small: 'max-[424px]:px-2',           // ≤424px
      standard: 'mobile:px-2',             // 425px-639px
    }
  },
  
  // Card/component padding
  card: {
    desktop: 'lg:p-8',
    tablet: 'md:p-6',
    mobile: {
      small: 'max-[424px]:p-4',            // ≤424px
      standard: 'mobile:p-4',              // 425px-639px
    }
  }
} as const;

// Margin configurations
export const margins = {
  // Section margins
  section: {
    bottom: {
      desktop: 'lg:mb-8',
      tablet: 'md:mb-4',
      mobile: {
        small: 'max-[424px]:mb-4',         // ≤424px
        standard: 'mobile:mb-4',           // 425px-639px
      }
    }
  },
  
  // Content margins
  content: {
    bottom: {
      desktop: 'lg:mb-6',
      tablet: 'md:mb-4',
      mobile: {
        small: 'max-[424px]:mb-3',         // ≤424px
        standard: 'mobile:mb-3',           // 425px-639px
      }
    }
  }
} as const;

// Utility functions to generate complete class strings
export const getContainerClasses = (type: keyof typeof containerWidths = 'hero') => {
  return [
    'relative z-10',
    containerWidths[type],
    containerWidths.mobile.small,
    containerWidths.mobile.standard,
    'mx-auto',
    containerPadding.tablet,
    containerPadding.mobile.small,
    containerPadding.mobile.standard,
    containerPadding.desktop
  ].join(' ');
};

export const getContentPaddingClasses = (type: 'text' | 'card' = 'text') => {
  const config = contentPadding[type];
  return [
    config.mobile.small,
    config.mobile.standard,
    config.tablet,
    config.desktop
  ].join(' ');
};

export const getSectionMarginClasses = () => {
  return [
    margins.section.bottom.mobile.small,
    margins.section.bottom.mobile.standard,
    margins.section.bottom.tablet,
    margins.section.bottom.desktop
  ].join(' ');
};

export const getContentMarginClasses = () => {
  return [
    margins.content.bottom.mobile.small,
    margins.content.bottom.mobile.standard,
    margins.content.bottom.tablet,
    margins.content.bottom.desktop
  ].join(' ');
};

// Specific configurations for different section types
export const sectionConfigs = {
  aboutHero: {
    container: [
      'relative z-10',
      containerWidths.hero,
      containerWidths.mobile.small,
      containerWidths.mobile.standard,
      'mx-auto',
      'px-6',                              // Unified padding across all breakpoints
      containerPadding.desktop
    ].join(' '),
    profileCard: [
      'max-w-md w-full'
    ].join(' '),
    textContent: [
      'max-w-2xl',
      'mx-auto lg:mx-0'
    ].join(' '),
    buttonContainer: [
      'max-w-2xl',
      'mx-auto lg:mx-0'
    ].join(' ')
  },
  
  skillsOverview: {
    container: [
      'relative z-10',
      containerWidths.hero,
      'mx-auto',
      'px-6',
      containerPadding.desktop
    ].join(' ')
  },

  projectsHero: {
    container: [
      'relative z-10',
      containerWidths.content,
      'mx-auto',
      'px-6',
      containerPadding.desktop
    ].join(' ')
  },

  journeySection: {
    container: getContainerClasses('hero'),
    headerContainer: [
      'max-w-6xl mx-auto',
      'px-6 md:px-8',
      'max-[424px]:px-8',               // ≤424px - Match 425px-639px spacing
      'mobile:px-8',                    // 425px-639px - Match beyond-the-code section content width
      'mb-20',
      'max-[424px]:mb-12',              // ≤424px
      'mobile:mb-12',                   // 425px-639px
      'sm:mb-16 md:mb-20'
    ].join(' '),
    timelineContainer: [
      'max-w-6xl mx-auto',
      'px-6 md:px-8',
      'max-[424px]:px-8',               // ≤424px - Match 425px-639px spacing
      'mobile:px-8',                    // 425px-639px - Match beyond-the-code section content width
      'sm:px-6'
    ].join(' '),
    cardSpacing: [
      'space-y-24',
      'max-[424px]:space-y-16',         // ≤424px
      'mobile:space-y-16',              // 425px-639px
      'sm:space-y-20 md:space-y-24 lg:space-y-40'
    ].join(' ')
  },

  whatDrivesMeSection: {
    container: getContainerClasses('wide'),
    headerContainer: [
      'max-w-7xl mx-auto',              // Updated to match beyondTheCodeSection
      'px-6 md:px-8',
      'max-[424px]:px-8',               // ≤424px - Match 425px-639px spacing
      'mobile:px-8',                    // 425px-639px
      'sm:px-5',
      'mb-12',
      'max-[424px]:mb-8',               // ≤424px
      'mobile:mb-8',                    // 425px-639px
      'sm:mb-10 md:mb-12 lg:mb-20'
    ].join(' '),
    contentContainer: [
      'max-w-7xl mx-auto',              // Updated to match beyondTheCodeSection
      'px-6 md:px-8',
      'max-[424px]:px-8',               // ≤424px - Match 425px-639px spacing
      'mobile:px-8',                    // 425px-639px - Match beyond-the-code section content width
      'sm:px-5'
    ].join(' '),
    passionSelector: [
      'mb-10',
      'mobile:mb-6',                    // 425px-639px
      'sm:mb-8 md:mb-10 lg:mb-16'
    ].join(' ')
  },

  beyondTheCodeSection: {
    container: getContainerClasses('wide'),
    headerContainer: [
      'max-w-7xl mx-auto',
      'px-6 md:px-8',
      'max-[424px]:px-4',               // ≤424px
      'mobile:px-4',                    // 425px-639px
      'mb-28',
      'max-[424px]:mb-12',              // ≤424px
      'mobile:mb-12',                   // 425px-639px
      'sm:mb-16 md:mb-20 lg:mb-28'
    ].join(' '),
    contentContainer: [
      'max-w-7xl mx-auto',
      'px-6 md:px-8',
      'max-[424px]:px-4',               // ≤424px
      'mobile:px-4',                    // 425px-639px
    ].join(' '),
    gridSpacing: [
      'gap-8',
      'max-[424px]:gap-4',              // ≤424px
      'mobile:gap-4',                   // 425px-639px
      'sm:gap-5 md:gap-6 lg:gap-8'
    ].join(' ')
  }
} as const;

// Export individual spacing values for custom use
export const spacingValues = {
  containerWidths,
  containerPadding,
  contentPadding,
  margins
} as const;
