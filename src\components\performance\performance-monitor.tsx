import { useEffect } from 'react'

interface PerformanceMetrics {
  fcp?: number
  lcp?: number
  fid?: number
  cls?: number
  ttfb?: number
}

export function PerformanceMonitor() {
  useEffect(() => {
    if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
      return
    }

    const metrics: PerformanceMetrics = {}

    // Measure First Contentful Paint (FCP)
    const measureFCP = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
        if (fcpEntry) {
          metrics.fcp = fcpEntry.startTime
          console.log(`🎨 First Contentful Paint: ${fcpEntry.startTime.toFixed(2)}ms`)
          observer.disconnect()
        }
      })
      observer.observe({ entryTypes: ['paint'] })
    }

    // Measure Largest Contentful Paint (LCP)
    const measureLCP = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        if (lastEntry) {
          metrics.lcp = lastEntry.startTime
          console.log(`🖼️ Largest Contentful Paint: ${lastEntry.startTime.toFixed(2)}ms`)
        }
      })
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    }

    // Measure First Input Delay (FID)
    const measureFID = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.processingStart && entry.startTime) {
            const fid = entry.processingStart - entry.startTime
            metrics.fid = fid
            console.log(`⚡ First Input Delay: ${fid.toFixed(2)}ms`)
          }
        })
      })
      observer.observe({ entryTypes: ['first-input'] })
    }

    // Measure Cumulative Layout Shift (CLS)
    const measureCLS = () => {
      let clsValue = 0
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        metrics.cls = clsValue
        console.log(`📐 Cumulative Layout Shift: ${clsValue.toFixed(4)}`)
      })
      observer.observe({ entryTypes: ['layout-shift'] })
    }

    // Measure Time to First Byte (TTFB)
    const measureTTFB = () => {
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigationEntry) {
        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart
        metrics.ttfb = ttfb
        console.log(`🌐 Time to First Byte: ${ttfb.toFixed(2)}ms`)
      }
    }

    // Resource loading performance
    const measureResourcePerformance = () => {
      const resources = performance.getEntriesByType('resource')
      const largeResources = resources.filter(resource => resource.transferSize > 100000) // > 100KB
      
      if (largeResources.length > 0) {
        console.warn('⚠️ Large Resources Detected:')
        largeResources.forEach(resource => {
          console.warn(`  ${resource.name}: ${(resource.transferSize / 1024).toFixed(2)}KB`)
        })
      }

      // Check for render-blocking resources
      const renderBlockingResources = resources.filter(resource => 
        resource.name.includes('.css') || resource.name.includes('.js')
      )
      
      console.log(`📦 Total Resources: ${resources.length}`)
      console.log(`🚫 Render-blocking Resources: ${renderBlockingResources.length}`)
    }

    // Bundle size analysis
    const analyzeBundleSize = () => {
      const jsResources = performance.getEntriesByType('resource').filter(resource => 
        resource.name.includes('.js')
      )
      
      const totalJSSize = jsResources.reduce((total, resource) => total + resource.transferSize, 0)
      console.log(`📊 Total JavaScript Size: ${(totalJSSize / 1024).toFixed(2)}KB`)
      
      if (totalJSSize > 500000) { // > 500KB
        console.warn('⚠️ JavaScript bundle is large. Consider code splitting.')
      }
    }

    // Memory usage monitoring
    const monitorMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        console.log(`🧠 Memory Usage:`)
        console.log(`  Used: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`)
        console.log(`  Total: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`)
        console.log(`  Limit: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`)
      }
    }

    // Initialize measurements
    measureFCP()
    measureLCP()
    measureFID()
    measureCLS()
    measureTTFB()

    // Delayed measurements for better accuracy
    setTimeout(() => {
      measureResourcePerformance()
      analyzeBundleSize()
      monitorMemoryUsage()
      
      // Summary report
      console.log('📈 Performance Summary:')
      console.log('  FCP:', metrics.fcp ? `${metrics.fcp.toFixed(2)}ms` : 'Not measured')
      console.log('  LCP:', metrics.lcp ? `${metrics.lcp.toFixed(2)}ms` : 'Not measured')
      console.log('  FID:', metrics.fid ? `${metrics.fid.toFixed(2)}ms` : 'Not measured')
      console.log('  CLS:', metrics.cls ? metrics.cls.toFixed(4) : 'Not measured')
      console.log('  TTFB:', metrics.ttfb ? `${metrics.ttfb.toFixed(2)}ms` : 'Not measured')
      
      // Performance score estimation
      let score = 100
      if (metrics.fcp && metrics.fcp > 1800) score -= 20
      if (metrics.lcp && metrics.lcp > 2500) score -= 25
      if (metrics.fid && metrics.fid > 100) score -= 20
      if (metrics.cls && metrics.cls > 0.1) score -= 15
      if (metrics.ttfb && metrics.ttfb > 600) score -= 10
      
      console.log(`🎯 Estimated Performance Score: ${Math.max(0, score)}/100`)
      
      if (score < 90) {
        console.warn('⚠️ Performance needs improvement. Check the metrics above.')
      } else {
        console.log('✅ Good performance!')
      }
    }, 3000)

    // Cleanup function
    return () => {
      // Performance observers are automatically cleaned up
    }
  }, [])

  return null // This component doesn't render anything
}

// Hook for component-level performance monitoring
export function useComponentPerformance(componentName: string) {
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return

    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      if (renderTime > 16) { // 60fps threshold
        console.warn(`⚠️ ${componentName} render time: ${renderTime.toFixed(2)}ms (>16ms)`)
      }
    }
  })
}
