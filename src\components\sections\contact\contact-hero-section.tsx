'use client'
// import React from 'react'
import { motion } from 'framer-motion'
import { ArrowRight, Mail, MessageSquare, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useProjectCounts } from '@/hooks/use-project-stats'

// Animation variants following the established pattern
const transitionVariants = {
  item: {
    hidden: {
      opacity: 0,
      filter: 'blur(12px)',
      y: 12,
    },
    visible: {
      opacity: 1,
      filter: 'blur(0px)',
      y: 0,
      transition: {
        type: 'spring' as const,
        bounce: 0.3,
        duration: 1.5,
      },
    },
  },
}

// Contact stats data - Updated for junior frontend developer
// Note: Projects count is now dynamic and will be updated in the component

export function ContactHeroSection() {
  // Get dynamic project counts
  const projectCounts = useProjectCounts();

  // Dynamic contact stats with real-time project count
  const contactStats = [
    {
      value: '24h',
      label: 'Response Time',
      description: 'Quick email responses',
      color: '#00FF85', // Neon green
      icon: <Mail className="w-4 h-4 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-4 sm:h-4 md:w-4 md:h-4 lg:w-5 lg:h-5" />
    },
    {
      value: projectCounts.completedWithPlus,
      label: 'Projects Built',
      description: 'Frontend learning projects',
      color: '#1E90FF', // Electric blue
      icon: <MessageSquare className="w-4 h-4 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-4 sm:h-4 md:w-4 md:h-4 lg:w-5 lg:h-5" />
    },
    {
      value: '100%',
      label: 'Passion Driven',
      description: 'Eager to learn & grow',
      color: '#FF0099', // Vivid pink
      icon: <Calendar className="w-4 h-4 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-4 sm:h-4 md:w-4 md:h-4 lg:w-5 lg:h-5" />
    }
  ];

  const scrollToForm = () => {
    const formElement = document.getElementById('contact-form')
    if (formElement) {
      formElement.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section className="relative bg-mono-bg min-h-screen flex items-center justify-center overflow-hidden">
      {/* Enhanced background effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-mono-bg via-mono-surface-dark/20 to-mono-bg" />

      {/* Floating background elements */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-[400px] h-[400px] bg-[#CF6FF4]/5 rounded-full blur-[100px]"
        initial={{ opacity: 0 }}
        animate={{
          y: [-8, 8],
          x: [-4, 4],
          opacity: [0.03, 0.06, 0.03],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-1/4 right-1/4 w-[300px] h-[300px] bg-[#8B31CD]/5 rounded-full blur-[80px]"
        initial={{ opacity: 0 }}
        animate={{
          y: [4, -4],
          x: [2, -2],
          opacity: [0.02, 0.05, 0.02],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
          delay: 2
        }}
      />

      <div className="relative z-10 max-w-4xl mx-auto px-8 max-[424px]:px-8 mobile:px-8 sm:px-8 md:px-8 lg:px-6 max-[424px]:max-w-4xl mobile:max-w-4xl py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32"> {/* Match featured-projects section content width */}
        <div className="text-center space-y-12 max-[424px]:space-y-8 mobile:space-y-8 sm:space-y-10 md:space-y-12 lg:space-y-16">
          {/* Main heading */}
          <motion.div
            variants={transitionVariants}
            initial="hidden"
            animate="visible"
            className="space-y-4 max-[424px]:space-y-3 mobile:space-y-3 sm:space-y-4 md:space-y-5 lg:space-y-6"
          >
            <motion.p
              className="text-mono-accent text-xs max-[424px]:text-xs mobile:text-xs md:text-xs lg:text-sm font-medium tracking-widest uppercase"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              Let's Collaborate
            </motion.p>

            <motion.h1
              className="text-4xl max-[424px]:text-3xl mobile:text-3xl sm:text-4xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight tracking-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Ready to Work{' '}
              <span className="relative inline-block">
                <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                  Together?
                </span>
                <motion.span
                  className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-15"
                  initial={{ opacity: 0.15 }}
                  animate={{
                    opacity: [0.15, 0.25, 0.15],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut"
                  }}
                  aria-hidden="true"
                />
              </span>
            </motion.h1>

            <motion.p
              className="text-base max-[424px]:text-sm mobile:text-sm sm:text-base md:text-base lg:text-xl text-mono-secondary max-w-3xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              I'm excited to collaborate on frontend projects and bring your ideas to life.
              Let's discuss how I can help build your next web application with modern React and TypeScript
            </motion.p>
          </motion.div>

          {/* Contact stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="grid grid-cols-3 max-[424px]:grid-cols-3 mobile:grid-cols-3 sm:grid-cols-3 md:grid-cols-3 gap-3 max-[424px]:gap-2 mobile:gap-2 sm:gap-3 md:gap-6 lg:gap-8 max-w-4xl mx-auto"
          >
            {contactStats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 30, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: 0.8 + index * 0.15,
                  type: "spring",
                  stiffness: 100,
                  damping: 20
                }}
                whileHover={{
                  y: -8,
                  transition: { duration: 0.3, ease: "easeOut" }
                }}
                className="group relative bg-mono-surface/[0.08] border border-mono-border rounded-xl max-[424px]:rounded-lg mobile:rounded-lg sm:rounded-xl md:rounded-xl lg:rounded-2xl p-4 max-[424px]:p-3 mobile:p-3 sm:p-4 md:p-5 lg:p-6 backdrop-blur-xl hover:bg-mono-surface/[0.15] hover:border-mono-border transition-all duration-500 cursor-pointer"
              >
                {/* Card glow effect */}
                <div
                  className="absolute inset-0 rounded-xl max-[424px]:rounded-lg mobile:rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-500 blur-xl"
                  style={{ backgroundColor: stat.color }}
                />

                <div className="relative z-10">
                  <div className="flex items-center justify-center mb-3 max-[424px]:mb-2 mobile:mb-2 sm:mb-3 md:mb-3 lg:mb-4">
                    <motion.div
                      className="p-2.5 max-[424px]:p-2 mobile:p-2 sm:p-2.5 md:p-2.5 lg:p-3 rounded-lg max-[424px]:rounded-md mobile:rounded-md sm:rounded-lg md:rounded-lg lg:rounded-xl"
                      style={{ backgroundColor: `${stat.color}20`, color: stat.color }}
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      {stat.icon}
                    </motion.div>
                  </div>
                  <motion.div
                    className="text-2xl max-[424px]:text-xl mobile:text-xl sm:text-2xl md:text-2xl lg:text-3xl font-bold mb-1.5 max-[424px]:mb-1 mobile:mb-1 sm:mb-1.5 md:mb-1.5 lg:mb-2 group-hover:scale-105 transition-transform duration-300"
                    style={{ color: stat.color }}
                  >
                    {stat.value}
                  </motion.div>
                  <h3 className="text-mono-text text-sm max-[424px]:text-xs mobile:text-xs sm:text-sm md:text-sm lg:text-base font-semibold mb-1 max-[424px]:mb-0.5 mobile:mb-0.5 sm:mb-0.5 md:mb-0.5 lg:mb-1 group-hover:text-white transition-colors duration-300">
                    {stat.label}
                  </h3>
                  <p className="text-mono-secondary text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm group-hover:text-mono-secondary transition-colors duration-300">
                    {stat.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="flex flex-row max-[424px]:flex-row mobile:flex-row sm:flex-row md:flex-row items-center justify-center gap-3 max-[424px]:gap-2 mobile:gap-2 sm:gap-3 md:gap-5 lg:gap-6"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                onClick={scrollToForm}
                className="relative bg-gradient-to-r from-[#CF6FF4] to-[#8B31CD] text-white font-semibold hover:from-[#CF6FF4]/90 hover:to-[#8B31CD]/90 transition-all duration-300 px-6 py-2.5 max-[424px]:px-4 max-[424px]:py-2 mobile:px-4 mobile:py-2 sm:px-6 sm:py-2.5 md:px-6 md:py-2.5 lg:px-8 lg:py-3 h-auto rounded-lg max-[424px]:rounded-md mobile:rounded-md sm:rounded-lg md:rounded-lg lg:rounded-xl overflow-hidden group text-sm max-[424px]:text-xs mobile:text-xs"
              >
                {/* Button glow effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4]/10 to-[#8B31CD]/10 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500"
                  aria-hidden="true"
                />
                <span className="relative z-10 flex items-center gap-1.5 max-[424px]:gap-1 mobile:gap-1">
                  <span className="max-[424px]:hidden mobile:hidden sm:inline">Start a Project</span>
                  <span className="max-[424px]:inline mobile:inline sm:hidden">Start Project</span>
                  <motion.div
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <ArrowRight className="w-3.5 h-3.5 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />
                  </motion.div>
                </span>
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                variant="outline"
                asChild
                className="border-mono-border hover:bg-mono-surface/20 hover:border-mono-accent/30 px-6 py-2.5 max-[424px]:px-4 max-[424px]:py-2 mobile:px-4 mobile:py-2 sm:px-6 sm:py-2.5 md:px-6 md:py-2.5 lg:px-8 lg:py-3 h-auto rounded-lg max-[424px]:rounded-md mobile:rounded-md sm:rounded-lg md:rounded-lg lg:rounded-xl transition-all duration-300 text-sm max-[424px]:text-xs mobile:text-xs"
              >
                <a href="mailto:<EMAIL>" className="flex items-center gap-1.5 max-[424px]:gap-1 mobile:gap-1">
                  <Mail className="w-3.5 h-3.5 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />
                  <span className="max-[424px]:hidden mobile:hidden sm:inline">Send Quick Email</span>
                  <span className="max-[424px]:inline mobile:inline sm:hidden">Quick Email</span>
                </a>
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
