import emailjs from '@emailjs/browser'

// EmailJS Configuration
// Replace these with your actual EmailJS credentials
export const EMAILJS_CONFIG = {
  SERVICE_ID: import.meta.env.VITE_EMAILJS_SERVICE_ID || 'your_service_id',
  TEMPLATE_ID: import.meta.env.VITE_EMAILJS_TEMPLATE_ID || 'your_template_id',
  PUBLIC_KEY: import.meta.env.VITE_EMAILJS_PUBLIC_KEY || 'your_public_key',
}

// Initialize EmailJS
export const initEmailJS = () => {
  emailjs.init(EMAILJS_CONFIG.PUBLIC_KEY)
}

// Email template parameters interface
export interface EmailTemplateParams extends Record<string, unknown> {
  from_name: string
  from_email: string
  project_type: string
  message: string
  to_name?: string
  reply_to?: string
}

// Send email function
export const sendEmail = async (templateParams: EmailTemplateParams): Promise<boolean> => {
  try {
    const response = await emailjs.send(
      EMAILJS_CONFIG.SERVICE_ID,
      EMAILJS_CONFIG.TEMPLATE_ID,
      templateParams,
      EMAILJS_CONFIG.PUBLIC_KEY
    )
    
    console.log('Email sent successfully:', response.status, response.text)
    return true
  } catch (error) {
    console.error('Failed to send email:', error)
    return false
  }
}

// Validate email format
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Rate limiting helper (simple client-side implementation)
const RATE_LIMIT_KEY = 'emailjs_last_sent'
const RATE_LIMIT_DURATION = 60000 // 1 minute

export const checkRateLimit = (): boolean => {
  const lastSent = localStorage.getItem(RATE_LIMIT_KEY)
  if (!lastSent) return true
  
  const timeDiff = Date.now() - parseInt(lastSent)
  return timeDiff > RATE_LIMIT_DURATION
}

export const setRateLimit = (): void => {
  localStorage.setItem(RATE_LIMIT_KEY, Date.now().toString())
}
