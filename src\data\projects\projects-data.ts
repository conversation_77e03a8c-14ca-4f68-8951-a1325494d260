/**
 * Centralized Project Data
 * Single source of truth for all portfolio projects
 * 
 * This file contains all project information in a unified format,
 * eliminating redundancy and ensuring consistency across components.
 */

import type { Project } from './types'
// import { DEFAULT_CARD_STYLES } from './constants'

/**
 * All portfolio projects with complete, consistent data
 * Using the comprehensive Project interface for type safety
 */
export const projects: readonly Project[] = [
  {
    // Core Information
    id: 'nexustore',
    title: 'NexuStore',
    description: 'A modern, full-featured e-commerce platform with 50+ products across 10 categories, secure authentication, and responsive design built for the Philippine market.',
    problemStatement: 'Premium e-commerce platform for Philippine market',
    
    // Visual Assets
    image: '/projects/nexustore.png',
    imageAlt: 'NexuStore e-commerce platform interface showing modern product catalog and shopping experience',
    
    // Technical Details
    technologies: ['React', 'TypeScript', 'Vite', 'Tailwind CSS', 'shadcn/ui', 'React Router'] as const,
    features: [
      'Product catalog with 50+ items',
      'Advanced search with filtering',
      'Shopping cart & wishlist',
      'Multi-step checkout process'
    ] as const,
    category: 'Frontend',
    
    // Links
    liveUrl: 'https://nexustore-phi.vercel.app/',
    githubUrl: 'https://github.com/christianjeraldjutba/nexustore',
    
    // Project Details
    status: 'Completed',
    duration: '4 weeks',
    team: 'Solo project',
    startDate: '2024-01-01',
    
    // Visual Styling
    accentColor: '#00FF85',
    cardStyle: {
      background: 'from-[#0A2A1C] to-[#071812]',
      glow: 'from-[#00FF85]/10 to-transparent'
    } as const,
    
    // Additional Details
    highlights: [
      'Seamless shopping experience',
      'Modern UI with shadcn/ui components',
      'Responsive design across all devices',
      'Optimized performance and accessibility'
    ] as const,
    keywords: [
      'React', 'TypeScript', 'E-commerce', 'Frontend', 'Tailwind CSS',
      'shadcn/ui', 'Shopping Cart', 'Product Catalog', 'Responsive Design'
    ] as const,
    
    // Flags and Sorting
    isFeatured: true,
    isPublic: true,
    priority: 95
  },
  {
    // Core Information
    id: 'weatherdash',
    title: 'WeatherDash',
    description: 'A professional weather dashboard with real-time weather data, 5-day forecasts, dark/light mode, and PWA capabilities optimized for all devices.',
    problemStatement: 'Modern weather dashboard with real-time data',
    
    // Visual Assets
    image: '/projects/weatherdash.png',
    imageAlt: 'WeatherDash weather dashboard interface showing current weather conditions and 5-day forecast',
    
    // Technical Details
    technologies: ['React', 'TypeScript', 'Vite', 'Tailwind CSS', 'OpenWeatherMap API'] as const,
    features: [
      'Real-time weather data & 5-day forecast',
      'Location search with autocomplete',
      'Dark/light mode with PWA support',
      'Responsive design & accessibility'
    ] as const,
    category: 'Frontend',
    
    // Links
    liveUrl: 'https://weather-dashboard-v1.vercel.app/',
    githubUrl: 'https://github.com/christianjeraldjutba/weatherdash',
    
    // Project Details
    status: 'Completed',
    duration: '3 weeks',
    team: 'Solo project',
    startDate: '2024-02-01',
    
    // Visual Styling
    accentColor: '#1E90FF',
    cardStyle: {
      background: 'from-[#0A1A2A] to-[#071218]',
      glow: 'from-[#1E90FF]/10 to-transparent'
    } as const,
    
    // Additional Details
    highlights: [
      'Beautiful data visualizations',
      'Smooth API integration',
      'PWA capabilities with offline support',
      'Intuitive user interface design'
    ] as const,
    keywords: [
      'React', 'TypeScript', 'Weather API', 'Frontend', 'Tailwind CSS',
      'shadcn/ui', 'PWA', 'Data Visualization', 'Real-time Data'
    ] as const,
    
    // Flags and Sorting
    isFeatured: false,
    isPublic: true,
    priority: 75
  },
  {
    // Core Information
    id: 'taskflow',
    title: 'TaskFlow',
    description: 'A clean, minimal task management application with Kanban boards, project organization, analytics dashboard, and drag-and-drop functionality for modern workflows.',
    problemStatement: 'Modern task management for productivity',
    
    // Visual Assets
    image: '/projects/taskflow.png',
    imageAlt: 'TaskFlow task management interface showing Kanban board with drag-and-drop functionality and project organization',
    
    // Technical Details
    technologies: ['React', 'TypeScript', 'Tailwind CSS', 'shadcn/ui', 'Framer Motion'] as const,
    features: [
      'Kanban boards with drag-and-drop',
      'Project organization & analytics',
      'Multiple view modes (List/Board)',
      'Smart notifications & insights'
    ] as const,
    category: 'Frontend',
    
    // Links
    liveUrl: 'https://taskflow-web-app.vercel.app/',
    githubUrl: 'https://github.com/christianjeraldjutba/taskflow',
    
    // Project Details
    status: 'Completed',
    duration: '5 weeks',
    team: 'Solo project',
    startDate: '2024-03-01',
    
    // Visual Styling
    accentColor: '#FF0099',
    cardStyle: {
      background: 'from-[#2A0A1A] to-[#180712]',
      glow: 'from-[#FF0099]/10 to-transparent'
    } as const,
    
    // Additional Details
    highlights: [
      'Intuitive drag-and-drop interface',
      'Comprehensive analytics dashboard',
      'Smooth animations with Framer Motion',
      'Modern task management features'
    ] as const,
    keywords: [
      'React', 'TypeScript', 'Task Management', 'Frontend', 'Tailwind CSS',
      'shadcn/ui', 'Framer Motion', 'Kanban', 'Productivity', 'Analytics'
    ] as const,
    
    // Flags and Sorting
    isFeatured: true,
    isPublic: true,
    priority: 85
  },
  {
    // Core Information
    id: 'portfolio-website',
    title: 'Portfolio Website',
    description: 'A modern, responsive portfolio website showcasing my work as a Frontend Developer. Built with React, TypeScript, and cutting-edge web technologies to deliver an exceptional user experience.',
    problemStatement: 'Professional portfolio showcasing frontend expertise',

    // Visual Assets
    image: '/projects/portfolio-website.png',
    imageAlt: 'Portfolio website interface showing modern design with dark theme, project showcase, and professional presentation',

    // Technical Details
    technologies: ['React', 'TypeScript', 'Vite', 'Tailwind CSS', 'Framer Motion', 'React Router'] as const,
    features: [
      'Modern dark theme with smooth animations',
      'Responsive design across all devices',
      'Interactive command palette (Cmd/Ctrl + K)',
      'Email integration with contact form'
    ] as const,
    category: 'Frontend',

    // Links
    liveUrl: 'https://cjjutba.site',
    githubUrl: 'https://github.com/cjjutba/personal-portfolio',

    // Project Details
    status: 'Completed',
    duration: '6 weeks',
    team: 'Solo project',
    startDate: '2024-04-01',

    // Visual Styling
    accentColor: '#8B5CF6',
    cardStyle: {
      background: 'from-[#1A0A2A] to-[#120718]',
      glow: 'from-[#8B5CF6]/10 to-transparent'
    } as const,

    // Additional Details
    highlights: [
      'Custom design system with consistent branding',
      'Advanced animations with Framer Motion',
      'Optimized performance and accessibility',
      'Professional presentation of technical skills'
    ] as const,
    keywords: [
      'React', 'TypeScript', 'Portfolio', 'Frontend', 'Tailwind CSS',
      'Framer Motion', 'Responsive Design', 'Professional Website', 'Dark Theme'
    ] as const,

    // Flags and Sorting
    isFeatured: true,
    isPublic: true,
    priority: 90
  },
  {
    // Core Information
    id: 'watchindex',
    title: 'WatchIndex',
    description: 'A modern, responsive movie & TV database application with advanced search, favorites system, and comprehensive media discovery. Built with React, TypeScript, and TMDB API integration.',
    problemStatement: 'Ultimate movie & TV database for media discovery',

    // Visual Assets
    image: '/projects/watch-index.png',
    imageAlt: 'WatchIndex movie and TV database interface showing modern cinema-inspired design with movie discovery features',

    // Technical Details
    technologies: ['React', 'TypeScript', 'Vite', 'Tailwind CSS', 'shadcn/ui', 'TanStack Query', 'TMDB API'] as const,
    features: [
      'Movie & TV show discovery with TMDB API',
      'Advanced search with real-time suggestions',
      'Favorites system with localStorage persistence',
      'Cinema-inspired design with dark theme'
    ] as const,
    category: 'Frontend',

    // Links
    liveUrl: 'https://watchindex.vercel.app/',
    githubUrl: 'https://github.com/cjjutba/watchindex',

    // Project Details
    status: 'Completed',
    duration: '4 weeks',
    team: 'Solo project',
    startDate: '2024-05-01',

    // Visual Styling
    accentColor: '#FFD700',
    cardStyle: {
      background: 'from-[#2A1A0A] to-[#1A1207]',
      glow: 'from-[#FFD700]/10 to-transparent'
    } as const,

    // Additional Details
    highlights: [
      'Comprehensive movie & TV database integration',
      'Advanced search with multi-type filtering',
      'Cinema-inspired UI with gold accents',
      'Optimized performance with React Query caching'
    ] as const,
    keywords: [
      'React', 'TypeScript', 'Movie Database', 'Frontend', 'Tailwind CSS',
      'shadcn/ui', 'TMDB API', 'TanStack Query', 'Media Discovery', 'Cinema'
    ] as const,

    // Flags and Sorting
    isFeatured: false,
    isPublic: true,
    priority: 88
  }
] as const

/**
 * Export individual projects for direct access
 */
export const nexustore = projects.find(p => p.id === 'nexustore')!
export const weatherdash = projects.find(p => p.id === 'weatherdash')!
export const taskflow = projects.find(p => p.id === 'taskflow')!
export const portfolioWebsite = projects.find(p => p.id === 'portfolio-website')!
export const watchindex = projects.find(p => p.id === 'watchindex')!

/**
 * Project metadata for quick reference
 */
export const PROJECT_METADATA = {
  totalProjects: projects.length,
  featuredProjects: projects.filter(p => p.isFeatured).length,
  completedProjects: projects.filter(p => p.status === 'Completed').length,
  inProgressProjects: projects.filter(p => p.status === 'In Progress').length,
  frontendProjects: projects.filter(p => p.category === 'Frontend').length,
  completedFrontendProjects: projects.filter(p => p.category === 'Frontend' && p.status === 'Completed').length,
  technologies: Array.from(new Set(projects.flatMap(p => p.technologies))),
  categories: Array.from(new Set(projects.map(p => p.category))),

  // Formatted display values for UI components
  display: {
    totalProjects: projects.length.toString(),
    totalProjectsWithPlus: `${projects.length}+`,
    completedProjects: projects.filter(p => p.status === 'Completed').length.toString(),
    completedProjectsWithPlus: `${projects.filter(p => p.status === 'Completed').length}+`,
    frontendProjects: projects.filter(p => p.category === 'Frontend').length.toString(),
    frontendProjectsWithPlus: `${projects.filter(p => p.category === 'Frontend').length}+`,
    completedFrontendProjects: projects.filter(p => p.category === 'Frontend' && p.status === 'Completed').length.toString(),
    completedFrontendProjectsWithPlus: `${projects.filter(p => p.category === 'Frontend' && p.status === 'Completed').length}+`,
    technologiesCount: Array.from(new Set(projects.flatMap(p => p.technologies))).length.toString()
  }
} as const
