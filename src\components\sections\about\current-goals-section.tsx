'use client'
import React, { useRef } from 'react'
import { motion, useScroll, useTransform, useSpring } from 'framer-motion'
import { Target, TrendingUp, Code2, Database, Palette } from 'lucide-react'

// Goal data interface
interface Goal {
  title: string
  description: string
  progress: number
  category: 'technical' | 'business' | 'design'
  icon: React.ReactNode
  color: string
  skills: string[]
}

// Current goals data
const currentGoals: Goal[] = [
  {
    title: 'MERN Stack Mastery',
    description: 'Deepening expertise in MongoDB, Express.js, React, and Node.js ecosystem for full-stack excellence',
    progress: 75,
    category: 'technical',
    icon: <Code2 className="w-6 h-6" />,
    color: '#00FF85',
    skills: ['Advanced React Patterns', 'Node.js Performance', 'MongoDB Optimization', 'Express Middleware']
  },
  {
    title: 'Business Solutions Focus',
    description: 'Building applications that solve real business problems and drive meaningful impact',
    progress: 60,
    category: 'business',
    icon: <TrendingUp className="w-6 h-6" />,
    color: '#1E90FF',
    skills: ['Requirements Analysis', 'Scalable Architecture', 'Performance Metrics', 'User Analytics']
  },
  {
    title: 'Database Optimization',
    description: 'Mastering database design, query optimization, and performance tuning techniques',
    progress: 65,
    category: 'technical',
    icon: <Database className="w-6 h-6" />,
    color: '#FF0099',
    skills: ['Query Optimization', 'Indexing Strategies', 'Database Design', 'Performance Monitoring']
  },
  {
    title: 'UX Principles',
    description: 'Understanding user experience design to create more intuitive and engaging interfaces',
    progress: 50,
    category: 'design',
    icon: <Palette className="w-6 h-6" />,
    color: '#8B31CD',
    skills: ['User Research', 'Design Systems', 'Accessibility', 'Interaction Design']
  }
]

// Enhanced goal card component
const GoalCard: React.FC<{ goal: Goal; index: number }> = ({ goal, index }) => {
  const cardRef = useRef<HTMLDivElement>(null)
  const [isHovered, setIsHovered] = React.useState(false)

  // Enhanced scroll animations
  const { scrollYProgress } = useScroll({
    target: cardRef,
    offset: ["start end", "end start"]
  })

  const y = useSpring(
    useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [50, 0, 0, 50]),
    { stiffness: 50, damping: 20 }
  )

  const opacity = useSpring(
    useTransform(scrollYProgress, [0, 0.1, 0.4, 0.9, 1], [0, 1, 1, 1, 0]),
    { stiffness: 50, damping: 20 }
  )

  const x = useSpring(
    useTransform(scrollYProgress, [0, 0.2], [index % 2 === 0 ? -50 : 50, 0]),
    { stiffness: 40, damping: 15 }
  )

  return (
    <motion.div
      ref={cardRef}
      style={{ opacity, x, y }}
      className="relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <motion.div
        className="relative bg-mono-surface/[0.08] border border-mono-border rounded-2xl p-8 backdrop-blur-xl hover:bg-mono-surface/20 transition-all duration-700"
        whileHover={{
          y: -4,
          scale: 1.005,
          transition: {
            type: "spring",
            stiffness: 300,
            damping: 25,
            mass: 0.8
          }
        }}
      >
        {/* Header with icon and progress */}
        <div className="flex items-start justify-between mb-6">
          <motion.div
            className="p-3 rounded-xl"
            style={{ backgroundColor: `${goal.color}20`, color: goal.color }}
            whileHover={{
              scale: 1.05,
              rotate: 3,
              transition: {
                type: "spring",
                stiffness: 400,
                damping: 20,
                mass: 0.4
              }
            }}
          >
            {goal.icon}
          </motion.div>
          
          {/* Progress indicator */}
          <div className="text-right">
            <motion.div
              className="text-2xl font-bold text-mono-text font-mono"
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.3 }
              }}
            >
              {goal.progress}%
            </motion.div>
            <div className="text-xs text-mono-secondary uppercase tracking-wide">Progress</div>
          </div>
        </div>

        {/* Title and description */}
        <div className="space-y-4 mb-6">
          <motion.h3
            className="text-xl font-bold text-mono-text tracking-tight"
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.3 }
            }}
          >
            {goal.title}
          </motion.h3>
          <p className="text-mono-secondary leading-relaxed text-sm">
            {goal.description}
          </p>
        </div>

        {/* Progress bar */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-xs text-mono-secondary uppercase tracking-wide">Current Focus</span>
          </div>
          <div className="relative h-2 bg-mono-surface-dark/20 rounded-full overflow-hidden">
            <motion.div
              className="absolute inset-y-0 left-0 rounded-full"
              style={{ backgroundColor: goal.color }}
              initial={{ width: 0 }}
              whileInView={{ width: `${goal.progress}%` }}
              transition={{ duration: 1.5, delay: index * 0.2, ease: "easeOut" }}
            />
            <motion.div
              className="absolute inset-y-0 left-0 rounded-full opacity-50"
              style={{ backgroundColor: goal.color }}
              animate={{
                width: [`${goal.progress}%`, `${Math.min(goal.progress + 10, 100)}%`, `${goal.progress}%`],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut"
              }}
            />
          </div>
        </div>

        {/* Skills list */}
        <div className="space-y-3">
          <span className="text-sm font-semibold text-mono-text block">Key Areas</span>
          <div className="flex flex-wrap gap-2">
            {goal.skills.map((skill, idx) => (
              <motion.span
                key={skill}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: idx * 0.1 }}
                className="px-3 py-1.5 bg-mono-surface/5 border border-mono-border/30 rounded-xl text-mono-text/60 text-xs hover:text-mono-text hover:border-mono-accent/50 transition-all duration-300 group relative overflow-hidden"
              >
                <motion.div
                  className="absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-500"
                  style={{ backgroundColor: goal.color }}
                  initial={false}
                  animate={{ scale: [1, 1.5, 1] }}
                  transition={{ duration: 3, repeat: Infinity }}
                />
                {skill}
              </motion.span>
            ))}
          </div>
        </div>

        {/* Card glow effect */}
        <motion.div
          className="absolute inset-0 opacity-0 rounded-2xl"
          style={{ backgroundColor: goal.color }}
          animate={{ opacity: isHovered ? 0.03 : 0 }}
          transition={{ duration: 0.3 }}
        />
      </motion.div>
    </motion.div>
  )
}

export function CurrentGoalsSection() {
  const sectionRef = useRef<HTMLDivElement>(null)

  // Section scroll animations
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start 0.9", "end 0.1"]
  })

  // Background effects
  const backgroundY1 = useSpring(
    useTransform(scrollYProgress, [0, 1], [-12, 12]),
    { stiffness: 50, damping: 25, restDelta: 0.001 }
  )
  const backgroundY2 = useSpring(
    useTransform(scrollYProgress, [0, 1], [8, -8]),
    { stiffness: 50, damping: 25, restDelta: 0.001 }
  )

  return (
    <section ref={sectionRef} className="relative bg-mono-bg py-32 overflow-hidden">
      {/* Solid background overlay */}
      <div className="absolute inset-0 bg-mono-bg -z-10" />
      
      {/* Enhanced background effects */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-1/4 left-1/4 w-[600px] h-[600px] bg-mono-surface/8 rounded-full blur-[120px]"
          style={{
            y: backgroundY1,
            willChange: "transform, opacity"
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-[800px] h-[800px] bg-mono-surface-light/8 rounded-full blur-[130px]"
          style={{
            y: backgroundY2,
            willChange: "transform, opacity"
          }}
        />

        {/* Floating element for depth */}
        <motion.div
          className="absolute top-1/2 left-1/2 w-[300px] h-[300px] bg-gradient-to-r from-[#00FF85]/3 to-[#1E90FF]/3 rounded-full blur-[60px] -translate-x-1/2 -translate-y-1/2"
          style={{
            y: useSpring(
              useTransform(scrollYProgress, [0, 1], [6, -6]),
              { stiffness: 40, damping: 30, restDelta: 0.001 }
            ),
            willChange: "transform, opacity"
          }}
        />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-6">
        {/* Section header */}
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 5 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.3,
              ease: "easeOut"
            }}
            viewport={{ once: true, margin: "-50px" }}
            className="space-y-3"
          >
            <p className="text-mono-accent text-sm font-medium tracking-widest uppercase">
              Current Focus
            </p>
            <h2 className="text-4xl md:text-5xl font-bold text-mono-text leading-tight tracking-tight">
              Learning{' '}
              <span className="relative inline-block">
                <span className="relative z-10 bg-gradient-to-r from-[#00FF85] to-[#1E90FF] text-transparent bg-clip-text italic font-cursive">
                  Roadmap
                </span>
                <motion.span
                  className="absolute inset-0 bg-gradient-to-r from-[#00FF85] to-[#1E90FF] blur-xl opacity-20"
                  animate={{
                    opacity: [0.15, 0.2, 0.15],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />
              </span>
            </h2>
          </motion.div>
        </div>

        {/* Goals grid */}
        <motion.div
          className="grid md:grid-cols-2 gap-8 mb-16"
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.4,
            ease: "easeOut"
          }}
          viewport={{ once: true, margin: "-50px" }}
        >
          {currentGoals.map((goal, index) => (
            <GoalCard key={goal.title} goal={goal} index={index} />
          ))}
        </motion.div>

        {/* Overall progress summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center bg-mono-surface/[0.08] border border-mono-border rounded-2xl p-8 backdrop-blur-xl"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <Target className="w-6 h-6 text-mono-accent" />
            <h3 className="text-xl font-bold text-mono-text">2024 Development Goals</h3>
          </div>
          <p className="text-mono-secondary mb-6 max-w-2xl mx-auto">
            Focused on becoming a well-rounded full-stack developer who can deliver 
            complete solutions from concept to deployment, with strong emphasis on 
            user experience and business value.
          </p>
          
          {/* Overall progress indicator */}
          <div className="flex items-center justify-center gap-4">
            <span className="text-sm text-mono-secondary">Overall Progress</span>
            <div className="flex-1 max-w-xs h-2 bg-mono-surface-dark/20 rounded-full overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-[#00FF85] to-[#1E90FF] rounded-full"
                initial={{ width: 0 }}
                whileInView={{ width: '62%' }}
                transition={{ duration: 2, delay: 0.5, ease: "easeOut" }}
              />
            </div>
            <span className="text-lg font-bold text-mono-text font-mono">62%</span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
