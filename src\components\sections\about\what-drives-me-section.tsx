'use client'
import React, { useRef, useState } from 'react'
import { motion, useScroll, useTransform, useSpring } from 'framer-motion'
import { Palette, Compass, Puzzle, Heart, ArrowRight } from 'lucide-react'
import { sectionConfigs } from '@/styles/spacing'

// Simple passion interface
interface Passion {
  title: string
  description: string
  color: string
  icon: React.ElementType
  index: number
}

// Natural passion data with simple, relatable content
const passions: Passion[] = [
  {
    title: 'Good Design',
    description: 'I love when websites look clean and work smoothly. When someone can use what I built without any trouble, that makes me really happy.',
    color: '#00FF85',
    icon: Palette,
    index: 0
  },
  {
    title: 'Easy to Use',
    description: 'Nothing feels better than watching someone use my website and just get it right away. No confusion, no frustration.',
    color: '#1E90FF',
    icon: Compass,
    index: 1
  },
  {
    title: 'Simple Solutions',
    description: 'I like taking complicated problems and making them simple. The best solutions are usually the ones that feel obvious.',
    color: '#FF0099',
    icon: Puzzle,
    index: 2
  },
  {
    title: 'Helping People',
    description: 'At the end of the day, I want to build things that actually help people. Whether it\'s saving time or making life easier.',
    color: '#8B31CD',
    icon: Heart,
    index: 3
  }
]

export function WhatDrivesMeSection() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [activeIndex, setActiveIndex] = useState<number | null>(null)

  // Refined scroll-based animations for background
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start 0.95", "end 0.05"]
  })

  const y1 = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, -15]),
    { stiffness: 80, damping: 20, restDelta: 0.0005 }
  )

  const y2 = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, 15]),
    { stiffness: 80, damping: 20, restDelta: 0.0005 }
  )

  return (
    <section ref={containerRef} className="relative bg-mono-bg py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32 overflow-hidden">
      {/* Enhanced background effects */}
      <div className="absolute inset-0">
        {/* Primary gradient orb */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-[600px] h-[600px] bg-gradient-to-br from-mono-surface/10 via-mono-surface/5 to-transparent rounded-full blur-[120px]"
          style={{
            y: y1,
            willChange: 'transform'
          }}
          initial={{ opacity: 0.6, scale: 1 }}
          animate={{
            opacity: [0.6, 0.8, 0.6],
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />

        {/* Secondary gradient orb */}
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-[800px] h-[800px] bg-gradient-to-tl from-mono-surface-light/8 via-mono-surface/6 to-transparent rounded-full blur-[140px]"
          style={{
            y: y2,
            willChange: 'transform'
          }}
          initial={{ opacity: 0.4, scale: 1.02 }}
          animate={{
            opacity: [0.4, 0.6, 0.4],
            scale: [1.02, 1, 1.02],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />

        {/* Subtle grid pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.02)_1px,transparent_0)] bg-[length:40px_40px] opacity-30" />
      </div>

      {/* Enhanced section header */}
      <div className={`relative z-10 ${sectionConfigs.whatDrivesMeSection.headerContainer} lg:max-w-4xl`}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.8,
            type: 'spring',
            stiffness: 100,
            damping: 20
          }}
          viewport={{ once: true, margin: "-15%" }}
          className="space-y-3 max-[424px]:space-y-2 mobile:space-y-2 sm:space-y-3 md:space-y-3 lg:space-y-4 text-center"
        >
          <motion.p
            className="text-mono-accent text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm font-semibold tracking-[0.2em] uppercase"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Passion and Purpose
          </motion.p>

          <h2 className="text-3xl max-[424px]:text-3xl mobile:text-3xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight tracking-tight">
            What{' '}
            <span className="relative inline-block">
              <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                Drives Me
              </span>
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-lg opacity-20"
                initial={{ opacity: 0.2 }}
                animate={{
                  opacity: [0.2, 0.3, 0.2],
                  scale: [1, 1.02, 1]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
              />
            </span>
          </h2>

          <motion.p
            className="text-mono-secondary text-sm max-[424px]:text-sm mobile:text-sm sm:text-sm md:text-lg max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
          </motion.p>
        </motion.div>
      </div>

      {/* Enhanced content layout */}
      <div className={`relative z-10 ${sectionConfigs.whatDrivesMeSection.contentContainer} lg:max-w-4xl`}>
        <div className="relative">
          {/* Premium passion selector */}
          <motion.div
            className={`flex justify-center ${sectionConfigs.whatDrivesMeSection.passionSelector}`}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            viewport={{ once: true }}
          >
            {/* Desktop and tablet layout (>=640px) - horizontal flex */}
            <div className="hidden sm:inline-flex flex-wrap justify-center gap-1.5 sm:gap-1.5 md:gap-1.5 lg:gap-2 p-1.5 sm:p-1.5 md:p-1.5 lg:p-2 bg-mono-surface-dark/30 backdrop-blur-md rounded-xl sm:rounded-xl md:rounded-xl lg:rounded-2xl border border-mono-border/50 shadow-2xl">
              {passions.map((passion, idx) => (
                <motion.button
                  key={passion.title}
                  className="relative px-3 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-2 md:py-2 lg:py-3 rounded-lg sm:rounded-lg md:rounded-lg lg:rounded-xl text-xs sm:text-xs md:text-xs lg:text-sm font-medium transition-all duration-300 hover:scale-105"
                  initial={{ scale: 1 }}
                  whileHover={{
                    scale: 1.05,
                    transition: { duration: 0.2 }
                  }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setActiveIndex(idx)}
                  onMouseEnter={() => setActiveIndex(idx)}
                >
                  {/* Enhanced active indicator */}
                  {activeIndex === idx && (
                    <motion.div
                      className="absolute inset-0 rounded-lg sm:rounded-lg md:rounded-lg lg:rounded-xl"
                      layoutId="activePassion"
                      style={{
                        background: `linear-gradient(135deg, ${passion.color}25, ${passion.color}15)`,
                        boxShadow: `0 0 20px ${passion.color}30`
                      }}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
                    >
                      <div
                        className="absolute inset-0 rounded-lg sm:rounded-lg md:rounded-lg lg:rounded-xl opacity-30"
                        style={{
                          background: `radial-gradient(circle at center, ${passion.color}50 0%, transparent 70%)`
                        }}
                      />
                    </motion.div>
                  )}

                  {/* Enhanced icon and text */}
                  <div className="relative z-10 flex items-center gap-1.5 sm:gap-1.5 md:gap-2 lg:gap-2">
                    <motion.div
                      animate={activeIndex === idx ? {
                        rotate: [0, 5, -5, 0],
                        scale: [1, 1.1, 1]
                      } : {}}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      {React.createElement(passion.icon, {
                        className: "w-3 h-3 sm:w-3 sm:h-3 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4",
                        style: {
                          color: activeIndex === idx ? passion.color : 'currentColor',
                          filter: activeIndex === idx ? `drop-shadow(0 0 8px ${passion.color}60)` : 'none'
                        }
                      })}
                    </motion.div>
                    <span
                      className="font-semibold"
                      style={{
                        color: activeIndex === idx ? passion.color : 'currentColor',
                        textShadow: activeIndex === idx ? `0 0 10px ${passion.color}40` : 'none'
                      }}
                    >
                      {passion.title}
                    </span>
                  </div>
                </motion.button>
              ))}
            </div>

            {/* Mobile layout (<640px) - 2x2 grid */}
            <div className="sm:hidden w-full max-[424px]:max-w-sm mobile:max-w-[340px] mx-auto">
              <div className="grid grid-cols-2 gap-1.5 p-1.5 bg-mono-surface-dark/30 backdrop-blur-md rounded-lg border border-mono-border/50 shadow-2xl">
                {passions.map((passion, idx) => (
                  <motion.button
                    key={passion.title}
                    className="relative px-2 py-1.5 rounded-md text-xs font-medium transition-all duration-300 hover:scale-105"
                    initial={{ scale: 1 }}
                    whileHover={{
                      scale: 1.05,
                      transition: { duration: 0.2 }
                    }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setActiveIndex(idx)}
                    onMouseEnter={() => setActiveIndex(idx)}
                  >
                    {/* Enhanced active indicator */}
                    {activeIndex === idx && (
                      <motion.div
                        className="absolute inset-0 rounded-md"
                        layoutId="activePassionMobile"
                        style={{
                          background: `linear-gradient(135deg, ${passion.color}25, ${passion.color}15)`,
                          boxShadow: `0 0 20px ${passion.color}30`
                        }}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
                      >
                        <div
                          className="absolute inset-0 rounded-md opacity-30"
                          style={{
                            background: `radial-gradient(circle at center, ${passion.color}50 0%, transparent 70%)`
                          }}
                        />
                      </motion.div>
                    )}

                    {/* Enhanced icon and text */}
                    <div className="relative z-10 flex items-center gap-1 justify-center">
                      <motion.div
                        animate={activeIndex === idx ? {
                          rotate: [0, 5, -5, 0],
                          scale: [1, 1.1, 1]
                        } : {}}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        {React.createElement(passion.icon, {
                          className: "w-2.5 h-2.5",
                          style: {
                            color: activeIndex === idx ? passion.color : 'currentColor',
                            filter: activeIndex === idx ? `drop-shadow(0 0 8px ${passion.color}60)` : 'none'
                          }
                        })}
                      </motion.div>
                      <span
                        className="font-semibold text-center"
                        style={{
                          color: activeIndex === idx ? passion.color : 'currentColor',
                          textShadow: activeIndex === idx ? `0 0 10px ${passion.color}40` : 'none'
                        }}
                      >
                        {passion.title}
                      </span>
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Premium glass display panel */}
          <div className="relative">
            <motion.div
              className="absolute inset-0 bg-gradient-to-br from-mono-surface-dark/20 via-mono-surface-dark/10 to-mono-surface-dark/5 backdrop-blur-xl rounded-2xl max-[424px]:rounded-xl mobile:rounded-xl sm:rounded-2xl md:rounded-3xl lg:rounded-3xl border border-mono-border/60 shadow-2xl overflow-hidden"
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              whileInView={{ opacity: 1, scale: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3, type: "spring", stiffness: 100 }}
              viewport={{ once: true }}
            >
              {/* Enhanced dynamic background */}
              <motion.div
                className="absolute inset-0 opacity-15"
                animate={{
                  background: activeIndex !== null
                    ? `radial-gradient(circle at 40% 40%, ${passions[activeIndex].color}40 0%, ${passions[activeIndex].color}20 30%, transparent 70%)`
                    : 'radial-gradient(circle at 50% 50%, rgba(255,255,255,0.02) 0%, transparent 50%)'
                }}
                transition={{ duration: 1, ease: "easeInOut" }}
              />

              {/* Premium decorative elements */}
              <div className="absolute top-0 right-0 w-32 h-32 max-[424px]:w-24 max-[424px]:h-24 mobile:w-24 mobile:h-24 sm:w-32 sm:h-32 md:w-48 md:h-48 lg:w-48 lg:h-48 bg-gradient-to-bl from-mono-surface/8 to-transparent rounded-full blur-2xl -translate-y-1/3 translate-x-1/3" />
              <div className="absolute bottom-0 left-0 w-40 h-40 max-[424px]:w-32 max-[424px]:h-32 mobile:w-32 mobile:h-32 sm:w-40 sm:h-40 md:w-64 md:h-64 lg:w-64 lg:h-64 bg-gradient-to-tr from-mono-surface-light/6 to-transparent rounded-full blur-3xl translate-y-1/3 -translate-x-1/3" />

              {/* Subtle grid overlay */}
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.03)_1px,transparent_0)] bg-[length:30px_30px] opacity-40" />
            </motion.div>

            {/* Enhanced content container */}
            <div className="relative py-8 max-[424px]:py-6 mobile:py-6 sm:py-8 md:py-10 lg:py-16 px-4 max-[424px]:px-4 mobile:px-3 sm:px-5 md:px-8 lg:px-12 xl:px-16">
              <div className="grid max-[424px]:grid-cols-1 mobile:grid-cols-1 sm:grid-cols-1 md:grid-cols-5 gap-6 max-[424px]:gap-4 mobile:gap-4 sm:gap-6 md:gap-8 lg:gap-12 items-center min-h-[200px] max-[424px]:min-h-[180px] mobile:min-h-[180px] sm:min-h-[200px] md:min-h-[260px] lg:min-h-[320px]">
                {/* Left side - Enhanced icon showcase */}
                <div className="max-[424px]:col-span-1 mobile:col-span-1 sm:col-span-1 md:col-span-2 flex justify-center max-[424px]:mb-4 mobile:mb-4 sm:mb-4 md:mb-0">
                  {activeIndex !== null && (
                    <motion.div
                      key={`icon-${activeIndex}`}
                      initial={{ opacity: 0, scale: 0.7, rotateY: -45 }}
                      animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                      exit={{ opacity: 0, scale: 0.7, rotateY: 45 }}
                      transition={{
                        duration: 0.6,
                        type: "spring",
                        stiffness: 120,
                        damping: 18
                      }}
                      className="relative"
                    >
                      <div className="relative">
                        {/* Enhanced multi-layer glow */}
                        <motion.div
                          className="absolute inset-0 rounded-full blur-[40px] max-[424px]:blur-[30px] mobile:blur-[30px] sm:blur-[40px] md:blur-[60px] lg:blur-[60px]"
                          style={{ backgroundColor: passions[activeIndex].color }}
                          animate={{
                            opacity: [0.4, 0.6, 0.4],
                            scale: [1, 1.15, 1]
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        />
                        <motion.div
                          className="absolute inset-0 rounded-full blur-[60px] max-[424px]:blur-[50px] mobile:blur-[50px] sm:blur-[60px] md:blur-[100px] lg:blur-[100px]"
                          style={{ backgroundColor: passions[activeIndex].color }}
                          animate={{
                            opacity: [0.2, 0.35, 0.2],
                            scale: [1.2, 1.4, 1.2]
                          }}
                          transition={{
                            duration: 4,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: 0.5
                          }}
                        />

                        {/* Premium icon with enhanced animations */}
                        <motion.div
                          animate={{
                            scale: [1, 1.08, 1],
                            rotate: [0, 8, 0, -8, 0],
                            y: [0, -5, 0]
                          }}
                          transition={{
                            duration: 8,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                          className="relative z-10"
                        >
                          {React.createElement(passions[activeIndex].icon, {
                            className: "w-20 h-20 max-[424px]:w-16 max-[424px]:h-16 mobile:w-16 mobile:h-16 sm:w-20 sm:h-20 md:w-28 md:h-28 lg:w-36 lg:h-36 xl:w-40 xl:h-40",
                            style: {
                              color: passions[activeIndex].color,
                              filter: `drop-shadow(0 0 25px ${passions[activeIndex].color}70) drop-shadow(0 0 50px ${passions[activeIndex].color}40)`
                            }
                          })}
                        </motion.div>
                      </div>
                    </motion.div>
                  )}
                </div>

                {/* Right side - Enhanced content */}
                <div className="max-[424px]:col-span-1 mobile:col-span-1 sm:col-span-1 md:col-span-3 max-[424px]:text-center mobile:text-center sm:text-center md:text-left">
                  {activeIndex !== null && (
                    <motion.div
                      key={`content-${activeIndex}`}
                      initial={{ opacity: 0, x: 40, y: 10 }}
                      animate={{ opacity: 1, x: 0, y: 0 }}
                      exit={{ opacity: 0, x: -40, y: -10 }}
                      transition={{
                        duration: 0.6,
                        type: "spring",
                        stiffness: 100,
                        damping: 20
                      }}
                      className="space-y-4 max-[424px]:space-y-3 mobile:space-y-3 sm:space-y-4 md:space-y-5 lg:space-y-8"
                    >
                      <motion.h3
                        className="text-xl max-[424px]:text-lg mobile:text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold tracking-tight"
                        style={{ color: passions[activeIndex].color }}
                        animate={{
                          textShadow: [
                            `0 0 15px ${passions[activeIndex].color}40`,
                            `0 0 25px ${passions[activeIndex].color}50`,
                            `0 0 15px ${passions[activeIndex].color}40`
                          ]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        {passions[activeIndex].title}
                      </motion.h3>

                      <motion.p
                        className="text-mono-secondary text-sm max-[424px]:text-sm mobile:text-sm sm:text-sm md:text-base lg:text-lg xl:text-xl leading-relaxed max-[424px]:leading-relaxed mobile:leading-relaxed sm:leading-relaxed md:leading-relaxed lg:leading-loose"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                      >
                        {passions[activeIndex].description}
                      </motion.p>

                      {/* Enhanced decorative element */}
                      <motion.div className="flex items-center gap-2 max-[424px]:gap-2 mobile:gap-2 sm:gap-2 md:gap-3 lg:gap-3 max-[424px]:justify-center mobile:justify-center sm:justify-center md:justify-start">
                        <motion.div
                          className="h-1 rounded-full"
                          style={{ backgroundColor: passions[activeIndex].color }}
                          animate={{
                            width: ["2rem", "3rem", "2rem"],
                            opacity: [0.8, 1, 0.8]
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        />
                        <motion.div
                          className="w-1.5 h-1.5 max-[424px]:w-1.5 max-[424px]:h-1.5 mobile:w-1.5 mobile:h-1.5 sm:w-1.5 sm:h-1.5 md:w-2 md:h-2 lg:w-2 lg:h-2 rounded-full"
                          style={{ backgroundColor: passions[activeIndex].color }}
                          animate={{
                            scale: [1, 1.5, 1],
                            opacity: [0.6, 1, 0.6]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: 0.5
                          }}
                        />
                      </motion.div>
                    </motion.div>
                  )}

                  {/* Enhanced default state */}
                  {activeIndex === null && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-center space-y-4 max-[424px]:space-y-3 mobile:space-y-3 sm:space-y-4 md:space-y-4 lg:space-y-6"
                    >
                      <p className="text-mono-secondary text-sm max-[424px]:text-sm mobile:text-sm sm:text-sm md:text-base lg:text-lg xl:text-xl leading-relaxed">
                        Click or hover over any passion above to see what drives me
                      </p>
                      <div className="flex justify-center">
                        <motion.div
                          animate={{
                            y: [0, -6, 0],
                            opacity: [0.6, 1, 0.6],
                            rotate: [0, 5, 0, -5, 0]
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        >
                          <ArrowRight className="w-5 h-5 max-[424px]:w-4 max-[424px]:h-4 mobile:w-4 mobile:h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 lg:w-6 lg:h-6 text-mono-accent" />
                        </motion.div>
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
