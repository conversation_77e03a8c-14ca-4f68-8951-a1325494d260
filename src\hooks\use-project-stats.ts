/**
 * Project Statistics Hook
 * Centralized hook for accessing dynamic project statistics across the application
 * 
 * This hook provides real-time project counts and statistics that automatically
 * update when projects are added, removed, or modified in the data layer.
 */

import { useMemo } from 'react'
import { PROJECT_METADATA, getAllProjects } from '@/data/projects'

/**
 * Interface for project statistics used throughout the application
 */
export interface ProjectStats {
  // Raw counts
  totalProjects: number
  completedProjects: number
  inProgressProjects: number
  featuredProjects: number
  frontendProjects: number
  completedFrontendProjects: number
  technologiesCount: number
  
  // Display-ready formatted values
  display: {
    totalProjects: string
    totalProjectsWithPlus: string
    completedProjects: string
    completedProjectsWithPlus: string
    frontendProjects: string
    frontendProjectsWithPlus: string
    completedFrontendProjects: string
    completedFrontendProjectsWithPlus: string
    technologiesCount: string
  }
  
  // Computed percentages and ratios
  completionRate: number
  frontendFocusPercentage: number
  
  // Helper methods for common use cases
  getProjectCountByStatus: (status: 'Completed' | 'In Progress' | 'Planned') => number
  getProjectCountByCategory: (category: 'Frontend' | 'Full-Stack' | 'Mobile') => number
  getFormattedCount: (count: number, withPlus?: boolean) => string
}

/**
 * Custom hook for accessing project statistics
 * 
 * @returns ProjectStats object with all project-related statistics
 */
export function useProjectStats(): ProjectStats {
  // Get fresh project data to ensure real-time updates
  const projects = getAllProjects()
  
  // Memoize calculations to prevent unnecessary re-computations
  const stats = useMemo(() => {
    const totalProjects = projects.length
    const completedProjects = projects.filter(p => p.status === 'Completed').length
    const inProgressProjects = projects.filter(p => p.status === 'In Progress').length
    const featuredProjects = projects.filter(p => p.isFeatured).length
    const frontendProjects = projects.filter(p => p.category === 'Frontend').length
    const completedFrontendProjects = projects.filter(p => p.category === 'Frontend' && p.status === 'Completed').length
    const technologiesCount = Array.from(new Set(projects.flatMap(p => p.technologies))).length
    
    // Calculate percentages
    const completionRate = totalProjects > 0 ? Math.round((completedProjects / totalProjects) * 100) : 0
    const frontendFocusPercentage = totalProjects > 0 ? Math.round((frontendProjects / totalProjects) * 100) : 0
    
    // Helper functions
    const getProjectCountByStatus = (status: 'Completed' | 'In Progress' | 'Planned') => {
      return projects.filter(p => p.status === status).length
    }
    
    const getProjectCountByCategory = (category: 'Frontend' | 'Full-Stack' | 'Mobile') => {
      return projects.filter(p => p.category === category).length
    }
    
    const getFormattedCount = (count: number, withPlus = false) => {
      return withPlus ? `${count}+` : count.toString()
    }
    
    return {
      // Raw counts
      totalProjects,
      completedProjects,
      inProgressProjects,
      featuredProjects,
      frontendProjects,
      completedFrontendProjects,
      technologiesCount,
      
      // Display-ready formatted values
      display: {
        totalProjects: totalProjects.toString(),
        totalProjectsWithPlus: `${totalProjects}+`,
        completedProjects: completedProjects.toString(),
        completedProjectsWithPlus: `${completedProjects}+`,
        frontendProjects: frontendProjects.toString(),
        frontendProjectsWithPlus: `${frontendProjects}+`,
        completedFrontendProjects: completedFrontendProjects.toString(),
        completedFrontendProjectsWithPlus: `${completedFrontendProjects}+`,
        technologiesCount: technologiesCount.toString()
      },
      
      // Computed percentages and ratios
      completionRate,
      frontendFocusPercentage,
      
      // Helper methods
      getProjectCountByStatus,
      getProjectCountByCategory,
      getFormattedCount
    }
  }, [projects])
  
  return stats
}

/**
 * Specialized hook for getting commonly used project counts
 * Provides quick access to the most frequently used statistics
 */
export function useProjectCounts() {
  const stats = useProjectStats()
  
  return useMemo(() => ({
    // Most commonly used counts
    total: stats.totalProjects,
    totalDisplay: stats.display.totalProjects,
    totalWithPlus: stats.display.totalProjectsWithPlus,
    
    completed: stats.completedProjects,
    completedDisplay: stats.display.completedProjects,
    completedWithPlus: stats.display.completedProjectsWithPlus,
    
    frontend: stats.frontendProjects,
    frontendDisplay: stats.display.frontendProjects,
    frontendWithPlus: stats.display.frontendProjectsWithPlus,
    
    completedFrontend: stats.completedFrontendProjects,
    completedFrontendDisplay: stats.display.completedFrontendProjects,
    completedFrontendWithPlus: stats.display.completedFrontendProjectsWithPlus,
    
    technologies: stats.technologiesCount,
    technologiesDisplay: stats.display.technologiesCount
  }), [stats])
}

/**
 * Hook for getting project statistics in a format suitable for stat cards
 * Returns an array of stat objects commonly used in UI components
 */
export function useProjectStatCards() {
  const stats = useProjectStats()
  
  return useMemo(() => [
    {
      value: stats.display.completedFrontendProjects,
      label: 'Projects Built',
      description: 'Completed Frontend Projects',
      count: stats.completedFrontendProjects
    },
    {
      value: 'React',
      label: 'Primary Focus',
      description: 'Modern Frontend Development',
      count: null
    },
    {
      value: '100%',
      label: 'Passion Driven',
      description: 'Continuous Learning',
      count: null
    }
  ], [stats])
}

/**
 * Legacy compatibility - provides the same interface as PROJECT_METADATA
 * but with real-time updates
 */
export function useProjectMetadata() {
  const stats = useProjectStats()
  
  return useMemo(() => ({
    ...PROJECT_METADATA,
    totalProjects: stats.totalProjects,
    featuredProjects: stats.featuredProjects,
    completedProjects: stats.completedProjects,
    inProgressProjects: stats.inProgressProjects,
    frontendProjects: stats.frontendProjects,
    completedFrontendProjects: stats.completedFrontendProjects,
    display: stats.display
  }), [stats])
}
