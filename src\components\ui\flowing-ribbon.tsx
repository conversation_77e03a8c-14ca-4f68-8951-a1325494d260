'use client'

import React from 'react'
import { motion } from 'framer-motion'

interface FlowingRibbonProps {
  keywords?: string[]
  primaryColor?: string
  secondaryColor?: string
  height?: string
  speed?: number
  angle?: number
  className?: string
}

const FlowingRibbon: React.FC<FlowingRibbonProps> = ({
  keywords = [
    'RESPONSIVE',
    'DYNAMIC',
    'SCALABLE',
    'SEARCH OPTIMIZED',
    'INTERACTIVE',
    'SECURE',
    'RELIABLE',
    'ENGAGING',
    'ACCESSIBLE',
    'MODERN',
    'CLEAN',
    'PROFESSIONAL'
  ],
  primaryColor = '#3B82F6', // Blue-500
  secondaryColor = '#1E40AF', // Blue-700
  height = '60px', // Reduced height
  speed = 50, // Slower speed for better readability
  angle = -3, // Very subtle 2 degree angle
  className = 'my-40' // Maximum spacing top and bottom for premium feel
}) => {
  // Create extended keyword list for seamless continuous loop - quadruple for smooth flow
  const extendedKeywords = [...keywords, ...keywords, ...keywords, ...keywords]

  return (
    <div className={`relative w-full ${className}`} style={{ height }}>
      {/* Main slanted rectangular ribbon */}
      <div
        className="absolute"
        style={{
          left: '50%',
          top: '50%',
          width: '100vw', // Full viewport width
          height: height,
          transform: `translate(-50%, -50%) skewY(${angle}deg)`,
          transformOrigin: 'center',
          zIndex: 2,
        }}
      >
        {/* Main ribbon background */}
        <motion.div
          className="relative w-full h-full overflow-hidden"
          style={{
            background: `linear-gradient(90deg, ${primaryColor} 0%, ${secondaryColor} 50%, ${primaryColor} 100%)`,
            boxShadow: '0 4px 20px rgba(59, 130, 246, 0.3)',
          }}
          initial={{ scaleX: 0, opacity: 0 }}
          animate={{ scaleX: 1, opacity: 1 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
          {/* Flowing text content */}
          <div className="absolute inset-0 flex items-center overflow-hidden">
            <motion.div
              className="flex items-center gap-8 whitespace-nowrap gpu-accelerated"
              animate={{
                x: [0, -2000]
              }}
              transition={{
                duration: speed,
                repeat: Infinity,
                ease: "linear",
                repeatType: "loop",
              }}
              style={{
                willChange: 'transform',
                transform: `skewY(${-angle}deg) translateZ(0)`,
                backfaceVisibility: 'hidden',
              }}
            >
              {extendedKeywords.map((keyword, index) => (
                <React.Fragment key={`${keyword}-${index}`}>
                  <motion.span
                    className="text-white font-bold text-sm md:text-base tracking-wider uppercase"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{
                      duration: 0.5,
                      delay: index * 0.05,
                      ease: "easeOut"
                    }}
                    style={{
                      textShadow: '0 2px 4px rgba(0,0,0,0.4)',
                      filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.6))'
                    }}
                  >
                    {keyword}
                  </motion.span>
                  {index < extendedKeywords.length - 1 && (
                    <motion.span
                      className="text-white/70 font-bold text-lg"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{
                        duration: 0.3,
                        delay: index * 0.05 + 0.1,
                        ease: "easeOut"
                      }}
                    >
                      +
                    </motion.span>
                  )}
                </React.Fragment>
              ))}
            </motion.div>
          </div>

          {/* Subtle highlight effect on top edge */}
          <div
            className="absolute top-0 left-0 w-full h-1 bg-white/20"
            style={{
              background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%)'
            }}
          />

          {/* Subtle shadow effect on bottom edge */}
          <div
            className="absolute bottom-0 left-0 w-full h-1 bg-black/20"
            style={{
              background: 'linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.3) 50%, transparent 100%)'
            }}
          />
        </motion.div>
      </div>

      {/* Mirror ribbon - exact duplicate but opposite angle for perfect X-crossing */}
      <div
        className="absolute"
        style={{
          left: '50%',
          top: '50%', // Same position as main ribbon
          width: '100vw', // Same width as main ribbon
          height: height, // Same height as main ribbon
          transform: `translate(-50%, -50%) skewY(${-angle}deg)`, // Opposite angle for X-crossing
          transformOrigin: 'center',
          zIndex: 1, // Behind main ribbon
        }}
      >
        {/* Mirror ribbon background - solid dark blue with reduced opacity */}
        <motion.div
          className="relative w-full h-full overflow-hidden"
          style={{
            background: 'linear-gradient(90deg, rgba(29, 78, 216, 0.7) 0%, rgba(30, 64, 175, 0.8) 50%, rgba(29, 78, 216, 0.7) 100%)',
            boxShadow: '0 2px 15px rgba(29, 78, 216, 0.2)',
            opacity: 0.75,
          }}
          initial={{ scaleX: 0, opacity: 0 }}
          animate={{ scaleX: 1, opacity: 0.75 }}
          transition={{ duration: 1.2, ease: "easeOut", delay: 0.15 }}
        >
          {/* Subtle flowing pattern for mirror ribbon */}
          <div
            className="absolute inset-0 opacity-20"
            style={{
              background: `repeating-linear-gradient(
                90deg,
                transparent 0px,
                rgba(255, 255, 255, 0.05) 25px,
                rgba(255, 255, 255, 0.1) 50px,
                transparent 75px
              )`,
              animation: `flowRight ${speed * 1.2}s linear infinite`,
            }}
          />

          {/* Highlight effect on top edge */}
          <div
            className="absolute top-0 left-0 w-full h-1 bg-white/20"
            style={{
              background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%)'
            }}
          />

          {/* Shadow effect on bottom edge */}
          <div
            className="absolute bottom-0 left-0 w-full h-1 bg-black/20"
            style={{
              background: 'linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.3) 50%, transparent 100%)'
            }}
          />
        </motion.div>
      </div>
    </div>
  )
}

export default FlowingRibbon
