'use client'
import React, { useRef } from 'react'
import { motion, useScroll, useTransform, useSpring, useMotionValue } from 'framer-motion'
import { ArrowRight } from 'lucide-react'
import { ScrollLink } from '@/components/ui/scroll-link'

// Story milestone interface
interface StoryMilestone {
  title: string
  description: string
  details: string[]
  color: string
}

// Story milestones data - Frontend development journey (junior level)
const storyMilestones: StoryMilestone[] = [
  {
    title: 'The Beginning',
    description: 'Started my frontend development journey during Computer Engineering studies, discovering the power of web technologies',
    details: [
      'First exposure to HTML, CSS, and JavaScript fundamentals',
      'Built simple static websites and learned responsive design',
      'Overcame challenges with limited resources and slow internet'
    ],
    color: 'from-[#00FF85] to-[#00CC6A]' // Neon green gradient
  },
  {
    title: 'Discovery & Growth',
    description: 'Discovered modern frontend frameworks and tools, focusing on React and component-based development',
    details: [
      'Learned React fundamentals and component architecture',
      'Explored CSS frameworks like TailwindCSS for efficient styling',
      'Built interactive projects with modern JavaScript (ES6+)'
    ],
    color: 'from-[#1E90FF] to-[#1873CC]' // Electric blue gradient
  },
  {
    title: 'Current Focus',
    description: 'Currently developing skills in modern frontend ecosystem while building real-world projects',
    details: [
      'Learning TypeScript for better code quality and maintainability',
      'Exploring Next.js for full-stack React applications',
      'Building portfolio projects and focusing on best practices'
    ],
    color: 'from-[#FF0099] to-[#CC007A]' // Vivid pink gradient
  }
]

// Premium milestone card with enhanced design
const StoryCard: React.FC<{ milestone: StoryMilestone; index: number }> = ({ milestone, index }) => {
  const cardRef = useRef<HTMLDivElement>(null)
  const [isHovered, setIsHovered] = React.useState(false)

  // Mouse tracking for premium hover effects
  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)
  const rotateX = useTransform(mouseY, [-300, 300], [1, -1])
  const rotateY = useTransform(mouseX, [-300, 300], [-1, 1])

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left - rect.width / 2
    const y = e.clientY - rect.top - rect.height / 2
    mouseX.set(x)
    mouseY.set(y)
  }

  const handleMouseLeave = () => {
    mouseX.set(0)
    mouseY.set(0)
    setIsHovered(false)
  }

  // Enhanced scroll animations with better timing
  const { scrollYProgress } = useScroll({
    target: cardRef,
    offset: ["start end", "end start"]
  })

  // Smooth vertical movement
  const y = useSpring(
    useTransform(scrollYProgress, 
      [0, 0.2, 0.8, 1], // Adjusted keyframes
      [50, 0, 0, 50]
    ),
    { stiffness: 50, damping: 20 }
  )

  // Extended fade animation for better visibility
  const opacity = useSpring(
    useTransform(scrollYProgress,
      [0, 0.1, 0.4, 0.9, 1], // More gradual fade in/out
      [0, 1, 1, 1, 0]
    ),
    { stiffness: 50, damping: 20 }
  )

  // Position-aware horizontal movement with adjusted timing
  const x = useSpring(
    useTransform(
      scrollYProgress,
      [0, 0.2], // Faster entry
      index % 2 === 1 ? [-100, 0] : [100, 0]
    ),
    { stiffness: 40, damping: 15 }
  )

  return (
    <motion.div
      ref={cardRef}
      style={{ opacity, x, y }}
      className={`relative flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'} gap-16 max-[424px]:gap-6 sm:gap-6 md:gap-8 lg:gap-16`}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onMouseEnter={() => setIsHovered(true)}
    >
      {/* Premium timeline connector with enhanced design */}
      <motion.div
        className={`hidden md:flex items-center flex-1 ${index % 2 === 0 ? 'justify-end' : 'justify-start'}`}
      >
        {index % 2 === 1 && (
          <motion.div
            className={`w-5 h-5 rounded-full bg-gradient-to-r ${milestone.color} relative flex items-center justify-center shadow-lg`}
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <motion.div
              className="absolute inset-0 rounded-full bg-inherit"
              animate={{
                scale: [1, 1.8, 1],
                opacity: [0.6, 0.1, 0.6],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="w-2.5 h-2.5 rounded-full bg-mono-bg"
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              transition={{ duration: 0.4, delay: 1.1 }}
            />
          </motion.div>
        )}
        <motion.div
          className={`h-[3px] w-full max-w-[200px] ${index % 2 === 0 ? 'bg-gradient-to-r' : 'bg-gradient-to-l'} ${milestone.color} opacity-30 rounded-full`}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1, delay: 0.4 }}
        />
        {index % 2 === 0 && (
          <motion.div
            className={`w-5 h-5 rounded-full bg-gradient-to-r ${milestone.color} relative flex items-center justify-center shadow-lg`}
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <motion.div
              className="absolute inset-0 rounded-full bg-inherit"
              animate={{
                scale: [1, 1.8, 1],
                opacity: [0.6, 0.1, 0.6],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="w-2.5 h-2.5 rounded-full bg-mono-bg"
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              transition={{ duration: 0.4, delay: 1.1 }}
            />
          </motion.div>
        )}
      </motion.div>

      {/* Enhanced premium card design */}
      <motion.div
        className="relative w-full max-[424px]:w-full mobile:w-full sm:w-full md:w-[450px] lg:w-[500px] bg-mono-surface-dark/8 backdrop-blur-md border border-mono-border/20 rounded-3xl max-[424px]:rounded-2xl mobile:rounded-2xl sm:rounded-2xl p-12 max-[424px]:p-4 mobile:p-4 sm:p-6 md:p-8 lg:p-12 hover:bg-mono-surface/8 hover:border-mono-border/40 transition-all duration-700 shadow-2xl hover:shadow-3xl"
        style={{
          rotateX,
          rotateY,
          transformStyle: "preserve-3d",
          transformPerspective: "2000px"
        }}
      >
        {/* Premium gradient overlay */}
        <motion.div
          className={`absolute inset-0 rounded-3xl max-[424px]:rounded-2xl sm:rounded-2xl bg-gradient-to-br ${milestone.color} opacity-0 transition-opacity duration-700`}
          animate={{ opacity: isHovered ? 0.03 : 0 }}
        />

        {/* Subtle inner glow */}
        <motion.div
          className="absolute inset-[1px] rounded-3xl max-[424px]:rounded-2xl sm:rounded-2xl bg-gradient-to-b from-white/[0.02] to-transparent opacity-0 transition-opacity duration-700"
          animate={{ opacity: isHovered ? 1 : 0 }}
        />

        {/* Premium content layout with enhanced spacing */}
        <div className="relative z-10 space-y-10 max-[424px]:space-y-4 mobile:space-y-4 sm:space-y-6 md:space-y-6 lg:space-y-10">
          {/* Sophisticated title design */}
          <div className="relative">
            <motion.h3
              className="text-3xl max-[424px]:text-xl mobile:text-xl sm:text-2xl md:text-2xl lg:text-4xl font-bold tracking-tight text-mono-text mb-3 max-[424px]:mb-2 mobile:mb-2 sm:mb-2 md:mb-2 lg:mb-3"
            >
              {milestone.title}
            </motion.h3>
            <motion.div
              className={`absolute -bottom-1 left-0 h-[3px] max-[424px]:h-[2px] mobile:h-[2px] bg-gradient-to-r ${milestone.color} rounded-full`}
              initial={{ width: 0 }}
              whileInView={{ width: '60px' }}
              transition={{ duration: 0.8, delay: 0.2 }}
            />
          </div>

          <p className="text-mono-text/80 leading-relaxed max-[424px]:leading-normal mobile:leading-normal tracking-wide text-lg max-[424px]:text-sm mobile:text-sm sm:text-base md:text-base lg:text-lg font-light max-w-md max-[424px]:max-w-full mobile:max-w-full">
            {milestone.description}
          </p>

          {/* Enhanced details list */}
          <div className="space-y-6 max-[424px]:space-y-3 mobile:space-y-3 sm:space-y-4 md:space-y-4 lg:space-y-6">
            {milestone.details.map((detail, idx) => (
              <motion.div
                key={idx}
                className="flex items-start gap-5 max-[424px]:gap-3 mobile:gap-3 sm:gap-3 md:gap-3 lg:gap-5 group"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ delay: idx * 0.15 + 0.3 }}
              >
                <motion.div
                  className={`mt-2 max-[424px]:mt-1 mobile:mt-1 sm:mt-1.5 md:mt-1.5 lg:mt-2 w-2 h-2 max-[424px]:w-1.5 max-[424px]:h-1.5 mobile:w-1.5 mobile:h-1.5 rounded-full bg-gradient-to-r ${milestone.color} flex-shrink-0`}
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    delay: idx * 0.3
                  }}
                />
                <span className="text-base max-[424px]:text-xs mobile:text-xs sm:text-sm md:text-sm lg:text-base text-mono-text/70 group-hover:text-mono-text/90 transition-colors duration-300 leading-relaxed max-[424px]:leading-normal mobile:leading-normal">
                  {detail}
                </span>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Sophisticated hover effect */}
        <motion.div
          className="absolute inset-0 rounded-2xl opacity-0 transition-opacity duration-700"
          style={{
            background: `radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), ${milestone.color.split(' ')[1].replace('to-[', '').replace(']', '')}15 0%, transparent 70%)`,
          }}
          animate={{ opacity: isHovered ? 0.15 : 0 }}
        />
      </motion.div>
    </motion.div>
  )
}

export function PersonalStorySection() {
  return (
    <section className="relative bg-mono-bg py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32 px-8 max-[424px]:px-8 mobile:px-8 overflow-hidden"> {/* Match beyond-the-code section content width */}
      {/* Enhanced premium background effects */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at 30% 20%, rgba(207,111,244,0.02) 0%, transparent 50%), radial-gradient(circle at 70% 80%, rgba(139,49,205,0.015) 0%, transparent 50%)',
          }}
          animate={{
            scale: [1, 1.05, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
        {/* Subtle grid pattern */}
        <div
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)',
            backgroundSize: '40px 40px'
          }}
        />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto lg:max-w-4xl lg:px-6">
        {/* Premium section header */}
        <div className="text-center mb-28 max-[424px]:mb-12 mobile:mb-12 sm:mb-16 md:mb-20 lg:mb-28">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-3 max-[424px]:space-y-2 mobile:space-y-2 sm:space-y-2 md:space-y-2 lg:space-y-3"
          >
            <p className="text-mono-accent text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs font-medium tracking-widest uppercase">
              My Journey
            </p>
            <h2 className="text-4xl max-[424px]:text-3xl mobile:text-3xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight tracking-tight">
              My{' '}
              <span className="relative inline-block">
                <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                  Growth
                </span>
                <motion.span
                  className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-20"
                  animate={{
                    opacity: [0.2, 0.3, 0.2],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />
              </span>
            </h2>
          </motion.div>
        </div>

        {/* Enhanced spacing for premium layout */}
        <div className="space-y-32 max-[424px]:space-y-12 mobile:space-y-12 sm:space-y-16 md:space-y-24 lg:space-y-40">
          {storyMilestones.map((milestone, index) => (
            <StoryCard key={milestone.title} milestone={milestone} index={index} />
          ))}
        </div>

        {/* Enhanced CTA Section with premium styling */}
        <div className="text-center mt-24 max-[424px]:mt-8 mobile:mt-8 sm:mt-12 md:mt-16 lg:mt-24">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: true }}
          >
            <ScrollLink
              to="/about"
              className="group relative overflow-hidden inline-flex items-center gap-3 max-[424px]:gap-2 mobile:gap-2 sm:gap-2.5 bg-transparent border-2 border-mono-border/30 hover:border-mono-accent/50 text-[#E0E0E0] font-medium px-6 max-[424px]:px-4 mobile:px-4 py-3 max-[424px]:py-2 mobile:py-2 sm:px-5 sm:py-2.5 md:px-5 md:py-2.5 lg:px-6 lg:py-3 rounded-xl transition-all duration-500"
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] to-[#8B31CD] opacity-0 group-hover:opacity-5 transition-opacity duration-500"
                initial={false}
                animate={{ scale: [1, 1.5, 1] }}
                transition={{ duration: 3, repeat: Infinity }}
              />
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4]/10 to-[#8B31CD]/10 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500"
              />
              <span className="relative z-10 text-base max-[424px]:text-sm mobile:text-sm sm:text-sm md:text-sm lg:text-base">Explore my journey</span>
              <motion.div
                className="relative"
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <ArrowRight className="w-4 h-4 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] to-[#8B31CD] blur-lg opacity-0 group-hover:opacity-50"
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
              </motion.div>
            </ScrollLink>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
