'use client'
import React, { useState, useRef, useMemo, useCallback } from 'react'
import { motion, useTransform, useScroll, useReducedMotion } from 'framer-motion'

// React Icons - High quality official icons for frontend-focused stack
import {
  FaReact,
  FaHtml5,
  FaCss3Alt,
  FaJs,
  FaGitAlt,
  FaGithub,
} from 'react-icons/fa'
import {
  SiTypescript,
  SiNextdotjs,
  SiTailwindcss,
  SiVite,
  SiVercel,
  SiEslint,
  SiPrettier,
} from 'react-icons/si'
import { VscCode } from 'react-icons/vsc'

// Technology data interface
interface Technology {
  name: string
  description: string
  color: string
  iconColor: string
  icon: React.ReactNode
  category: 'Core' | 'Framework' | 'Tool'
}



// Tech stack data for pill-style layout
const techStackData: Technology[] = [
  {
    name: 'HTML5',
    description: 'Semantic markup and structure',
    color: 'text-mono-text',
    iconColor: '#E34F26',
    icon: <FaHtml5 className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Core'
  },
  {
    name: 'CSS3',
    description: 'Modern styling and layouts',
    color: 'text-mono-text',
    iconColor: '#1572B6',
    icon: <FaCss3Alt className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Core'
  },
  {
    name: 'JavaScript',
    description: 'Dynamic web interactions',
    color: 'text-mono-text',
    iconColor: '#F7DF1E',
    icon: <FaJs className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Core'
  },
  {
    name: 'TailwindCSS',
    description: 'Utility-first CSS framework',
    color: 'text-mono-text',
    iconColor: '#38B2AC',
    icon: <SiTailwindcss className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Core'
  },
  {
    name: 'React',
    description: 'Component-based UI development',
    color: 'text-mono-text',
    iconColor: '#61DAFB',
    icon: <FaReact className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Framework'
  },
  {
    name: 'TypeScript',
    description: 'Type-safe JavaScript development',
    color: 'text-mono-text',
    iconColor: '#3178C6',
    icon: <SiTypescript className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Framework'
  },
  {
    name: 'Next.js',
    description: 'Learning React framework',
    color: 'text-mono-text',
    iconColor: '#FFFFFF',
    icon: <SiNextdotjs className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Framework'
  },
  {
    name: 'Git',
    description: 'Version control system',
    color: 'text-mono-text',
    iconColor: '#F05032',
    icon: <FaGitAlt className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Tool'
  },
  {
    name: 'GitHub',
    description: 'Code collaboration platform',
    color: 'text-mono-text',
    iconColor: '#FFFFFF',
    icon: <FaGithub className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Tool'
  },
  {
    name: 'VS Code',
    description: 'Primary code editor',
    color: 'text-mono-text',
    iconColor: '#007ACC',
    icon: <VscCode className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Tool'
  },
  {
    name: 'Vite',
    description: 'Fast build tool and dev server',
    color: 'text-mono-text',
    iconColor: '#646CFF',
    icon: <SiVite className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Tool'
  },
  {
    name: 'ESLint',
    description: 'Code quality and consistency',
    color: 'text-mono-text',
    iconColor: '#4B32C3',
    icon: <SiEslint className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Tool'
  },
  {
    name: 'Prettier',
    description: 'Code formatting and style',
    color: 'text-mono-text',
    iconColor: '#F7B93E',
    icon: <SiPrettier className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Tool'
  },
  {
    name: 'Vercel',
    description: 'Deployment platform',
    color: 'text-mono-text',
    iconColor: '#FFFFFF',
    icon: <SiVercel className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />,
    category: 'Tool'
  }
]

// Enhanced animation variants with performance considerations and parallax
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.06,
      delayChildren: 0.15,
      duration: 0.8
    }
  }
}

const itemVariants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.95,
    rotateX: 15
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    rotateX: 0,
    transition: {
      type: 'spring' as const,
      stiffness: 140,
      damping: 18,
      mass: 0.9
    }
  }
}

// Performance-optimized spring configs
const springConfig = {
  type: "spring" as const,
  stiffness: 200,
  damping: 25,
  mass: 0.8
}

const smoothSpringConfig = {
  type: "spring" as const,
  stiffness: 150,
  damping: 20,
  mass: 1
}



// TechPill component matching the screenshot style
const TechPill: React.FC<{ tech: Technology; index: number }> = React.memo(({ tech, index }) => {
  const [isHovered, setIsHovered] = useState(false)
  const prefersReducedMotion = useReducedMotion()

  const handleMouseEnter = useCallback(() => setIsHovered(true), [])
  const handleMouseLeave = useCallback(() => setIsHovered(false), [])

  return (
    <motion.div
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      custom={index}
      className="relative group"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <motion.div
        className="relative flex items-center gap-2.5 max-[424px]:gap-2 mobile:gap-2 sm:gap-2 md:gap-2 lg:gap-2.5 px-3 max-[424px]:px-3 min-[425px]:max-[639px]:px-3 py-2.5 max-[424px]:py-2 mobile:py-2 sm:px-3 sm:py-2 md:px-3 md:py-2 lg:px-4 lg:py-2.5 rounded-full bg-mono-surface/[0.08] backdrop-blur-sm border border-mono-border/25 shadow-sm"
        animate={{
          scale: !prefersReducedMotion && isHovered ? 1.03 : 1,
          y: !prefersReducedMotion && isHovered ? -2 : 0,
          rotateY: !prefersReducedMotion && isHovered ? 2 : 0,
          borderColor: isHovered ? 'rgba(255, 255, 255, 0.18)' : 'rgba(255, 255, 255, 0.08)',
          backgroundColor: isHovered ? 'rgba(255, 255, 255, 0.14)' : 'rgba(255, 255, 255, 0.06)',
          boxShadow: isHovered
            ? '0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(255, 255, 255, 0.08)'
            : '0 2px 8px rgba(0, 0, 0, 0.06)'
        }}
        transition={springConfig}
        style={{
          willChange: 'transform, border-color, background-color, box-shadow',
          transformStyle: 'preserve-3d'
        }}
      >
        {/* Technology Icon with enhanced animations */}
        <motion.div
          className="flex items-center justify-center relative flex-shrink-0"
          style={{ color: tech.iconColor }}
          animate={{
            scale: !prefersReducedMotion && isHovered ? 1.15 : 1,
            rotate: !prefersReducedMotion && isHovered ? 8 : 0,
            rotateY: !prefersReducedMotion && isHovered ? 10 : 0,
          }}
          transition={smoothSpringConfig}
        >
          {tech.icon}

          {/* Enhanced glow effect */}
          <motion.div
            className="absolute inset-0 blur-lg opacity-0 -z-10 rounded-full"
            style={{ backgroundColor: tech.iconColor }}
            animate={{
              opacity: isHovered ? 0.35 : 0.12,
              scale: isHovered ? 1.8 : 1.2
            }}
            transition={{ duration: 0.4 }}
          />

          {/* Pulse effect on hover */}
          <motion.div
            className="absolute inset-0 rounded-full border-2 opacity-0"
            style={{ borderColor: tech.iconColor }}
            animate={{
              opacity: isHovered ? [0, 0.6, 0] : 0,
              scale: isHovered ? [1, 1.5, 2] : 1
            }}
            transition={{
              duration: 0.8,
              repeat: isHovered ? Infinity : 0
            }}
          />
        </motion.div>

        {/* Technology Name with enhanced animation */}
        <motion.span
          className="font-medium text-mono-text text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm whitespace-nowrap"
          animate={{
            x: !prefersReducedMotion && isHovered ? 2 : 0,
            color: isHovered ? '#ffffff' : 'var(--mono-text)'
          }}
          transition={smoothSpringConfig}
        >
          {tech.name}
        </motion.span>

        {/* Enhanced hover glow */}
        <motion.div
          className="absolute inset-0 opacity-0 rounded-full pointer-events-none"
          style={{ backgroundColor: tech.iconColor }}
          animate={{
            opacity: isHovered ? 0.05 : 0
          }}
          transition={smoothSpringConfig}
        />
      </motion.div>

      {/* Enhanced floating shadow with depth */}
      <motion.div
        className="absolute inset-0 bg-mono-surface-dark/10 rounded-full blur-md -z-10"
        animate={{
          scale: isHovered ? 1.08 : 0.96,
          opacity: isHovered ? 0.2 : 0.06,
          y: isHovered ? 4 : 1
        }}
        transition={smoothSpringConfig}
        style={{
          willChange: 'transform, opacity'
        }}
      />

      {/* Subtle floating animation */}
      {!prefersReducedMotion && (
        <motion.div
          className="absolute inset-0 -z-20"
          animate={{
            y: [0, -1, 0],
          }}
          transition={{
            duration: 3 + (index * 0.2),
            repeat: Infinity,
            delay: index * 0.1
          }}
        />
      )}
    </motion.div>
  )
})



// Optimized main section component with enhanced performance and parallax
export function TechStackSection() {
  const containerRef = useRef<HTMLDivElement>(null)
  const prefersReducedMotion = useReducedMotion()

  // Parallax scroll effects
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  })

  // Smooth parallax transforms
  const y1 = useTransform(scrollYProgress, [0, 1], [0, -50])
  const y2 = useTransform(scrollYProgress, [0, 1], [0, 50])
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0])
  const scale = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.95, 1, 1, 0.95])

  // Memoized background animations for better performance
  const backgroundAnimations = useMemo(() => ({
    blob1: {
      y: prefersReducedMotion ? 0 : [-12, 12],
      x: prefersReducedMotion ? 0 : [-8, 8],
      opacity: prefersReducedMotion ? 0.06 : [0.08, 0.05],
      scale: prefersReducedMotion ? 1 : [1, 1.05],
      transition: {
        duration: 6,
        repeat: Infinity,
        repeatType: "reverse" as const
      }
    },
    blob2: {
      y: prefersReducedMotion ? 0 : [8, -12],
      x: prefersReducedMotion ? 0 : [6, -6],
      opacity: prefersReducedMotion ? 0.06 : [0.05, 0.08],
      scale: prefersReducedMotion ? 1 : [1.02, 0.98],
      transition: {
        duration: 7,
        repeat: Infinity,
        repeatType: "reverse" as const
      }
    },
    blob3: {
      y: prefersReducedMotion ? 0 : [-6, 10],
      x: prefersReducedMotion ? 0 : [4, -8],
      opacity: prefersReducedMotion ? 0.04 : [0.06, 0.03],
      scale: prefersReducedMotion ? 1 : [0.95, 1.08],
      transition: {
        duration: 8,
        repeat: Infinity,
        repeatType: "reverse" as const
      }
    }
  }), [prefersReducedMotion])

  return (
    <motion.section
      className="relative bg-mono-bg overflow-hidden py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32"
      ref={containerRef}
      style={{ opacity, scale }}
    >
      {/* Enhanced background effects with parallax and better performance */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Primary animated blob with parallax */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-[600px] h-[600px] bg-mono-surface/5 rounded-full blur-[120px]"
          initial={{ opacity: 0 }}
          animate={backgroundAnimations.blob1}
          style={{
            willChange: 'transform, opacity',
            y: y1
          }}
        />

        {/* Secondary animated blob with opposite parallax */}
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-[800px] h-[800px] bg-mono-surface-light/5 rounded-full blur-[130px]"
          initial={{ opacity: 0 }}
          animate={backgroundAnimations.blob2}
          style={{
            willChange: 'transform, opacity',
            y: y2
          }}
        />

        {/* Tertiary floating element */}
        <motion.div
          className="absolute top-1/2 left-1/2 w-[400px] h-[400px] bg-gradient-to-r from-mono-accent/[0.03] to-transparent rounded-full blur-[100px] -translate-x-1/2 -translate-y-1/2"
          initial={{ opacity: 0 }}
          animate={backgroundAnimations.blob3}
          style={{
            willChange: 'transform, opacity',
            y: useTransform(scrollYProgress, [0, 1], [0, -30])
          }}
        />

        {/* Enhanced gradient overlays */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-mono-surface/[0.02] to-transparent" />
        <div className="absolute inset-0 bg-gradient-to-tr from-mono-accent/[0.01] via-transparent to-mono-surface/[0.01]" />
      </div>

      {/* Enhanced section header with parallax and optimized animations - Match beyond-the-code section content width */}
      <motion.div
        className="relative z-10 max-w-4xl mx-auto px-8 max-[424px]:px-8 mobile:px-8 md:px-8 lg:px-6 mb-20 max-[424px]:mb-12 mobile:mb-12 md:mb-14 lg:mb-20 max-[424px]:max-w-4xl mobile:max-w-4xl"
        style={{ y: useTransform(scrollYProgress, [0, 1], [0, -20]) }}
      >
        <motion.div
          initial={{ opacity: 0, y: 12 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.6
          }}
          viewport={{ once: true, margin: "-100px" }}
          className="space-y-4 max-[424px]:space-y-3 mobile:space-y-3 md:space-y-3 lg:space-y-4 text-center"
        >
          <motion.p
            className="text-mono-accent text-sm font-medium tracking-widest uppercase"
            initial={{ opacity: 0, y: 8 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1, duration: 0.5 }}
            viewport={{ once: true }}
          >
            Tech Stack
          </motion.p>
          <motion.h2
            className="text-4xl max-[424px]:text-3xl mobile:text-3xl sm-tablet:text-3xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight max-[424px]:leading-tight mobile:leading-tight tracking-tight"
            initial={{ opacity: 0, y: 15 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            viewport={{ once: true }}
          >
            Core{' '}
            <span className="relative inline-block">
              <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                Technologies
              </span>
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-20"
                animate={{
                  opacity: prefersReducedMotion ? 0.18 : [0.15, 0.28, 0.15],
                  scale: prefersReducedMotion ? 1 : [1, 1.02, 1],
                }}
                transition={{
                  duration: 4,
                  repeat: prefersReducedMotion ? 0 : Infinity,
                  repeatType: "reverse"
                }}
              />
            </span>
          </motion.h2>
        </motion.div>
      </motion.div>

      {/* Pill-style layout with parallax effects - Match beyond-the-code section content width */}
      <motion.div
        className="relative max-w-4xl mx-auto px-8 max-[424px]:px-8 mobile:px-8 md:px-8 lg:px-6 max-[424px]:max-w-4xl mobile:max-w-4xl"
        style={{ y: useTransform(scrollYProgress, [0, 1], [0, 10]) }}
      >
        {/* Tech stack pills in flexible rows with enhanced animations */}
        <motion.div
          className="flex flex-wrap justify-center items-center gap-3 max-[424px]:gap-3 mobile:gap-3 sm:gap-2.5 md:gap-2.5 lg:gap-4"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-120px" }}
        >
          {techStackData.map((tech, index) => (
            <TechPill key={tech.name} tech={tech} index={index} />
          ))}
        </motion.div>

        {/* Subtle floating particles for enhanced visual appeal */}
        {!prefersReducedMotion && (
          <>
            <motion.div
              className="absolute top-1/4 left-1/4 w-2 h-2 bg-mono-accent/20 rounded-full blur-sm"
              animate={{
                y: [-10, 10],
                x: [-5, 5],
                opacity: [0.3, 0.6, 0.3]
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />
            <motion.div
              className="absolute bottom-1/3 right-1/3 w-1.5 h-1.5 bg-mono-surface-light/30 rounded-full blur-sm"
              animate={{
                y: [8, -8],
                x: [3, -3],
                opacity: [0.2, 0.5, 0.2]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                repeatType: "reverse",
                delay: 2
              }}
            />
          </>
        )}
      </motion.div>
    </motion.section>
  )
}