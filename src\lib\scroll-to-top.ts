/**
 * Scroll to top utility functions for smooth navigation
 * Provides consistent scroll behavior across the application
 */

export interface ScrollToTopOptions {
  behavior?: 'smooth' | 'instant' | 'auto'
  block?: 'start' | 'center' | 'end' | 'nearest'
  inline?: 'start' | 'center' | 'end' | 'nearest'
}

/**
 * Scrolls to the top of the page with smooth animation
 * @param options - Scroll behavior options
 */
export const scrollToTop = (options: ScrollToTopOptions = {}) => {
  const {
    behavior = 'smooth'
  } = options

  // Use scrollTo for better browser support
  if (typeof window !== 'undefined') {
    if (behavior === 'smooth') {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth'
      })
    } else {
      // For instant scrolling or fallback
      window.scrollTo(0, 0)
    }
  }
}

/**
 * React hook for scroll to top functionality
 * Returns a function that can be called to scroll to top
 */
export const useScrollToTop = (options: ScrollToTopOptions = {}) => {
  return () => scrollToTop(options)
}

/**
 * Higher-order function that wraps a navigation handler with scroll to top
 * @param handler - Original click handler (optional)
 * @param options - Scroll options
 */
export const withScrollToTop = (
  handler?: () => void,
  options: ScrollToTopOptions = {}
) => {
  return () => {
    // Execute scroll to top first
    scrollToTop(options)
    
    // Then execute the original handler if provided
    if (handler) {
      handler()
    }
  }
}

/**
 * Delay scroll to top - useful for route transitions
 * @param delay - Delay in milliseconds (default: 100ms)
 * @param options - Scroll options
 */
export const scrollToTopDelayed = (
  delay: number = 100,
  options: ScrollToTopOptions = {}
) => {
  setTimeout(() => {
    scrollToTop(options)
  }, delay)
}
