import { useLocation } from 'react-router-dom';
import { ScrollLink } from '@/components/ui/scroll-link';
import { cn } from '@/lib/utils';

const menuItems = [
  { name: 'Home', href: '/' },
  { name: 'About', href: '/about' },
  { name: 'Skills', href: '/skills' },
  { name: 'Projects', href: '/projects' },
  // { name: 'Blog', href: '/blog' },
  { name: 'Contact', href: '/contact' },
];

interface NavLinksProps {
  isMobile?: boolean;
}

export function NavLinks({ isMobile = false }: NavLinksProps) {
  const location = useLocation();

  if (isMobile) {
    return (
      <div className="lg:hidden">
        <ul className="space-y-6 text-base">
          {menuItems.map((item, index) => (
            <li key={index}>
              <ScrollLink
                to={item.href}
                className={cn(
                  "block duration-300 font-medium transition-all px-4 py-2.5 rounded-xl hover:scale-105",
                  location.pathname === item.href
                    ? "text-mono-text bg-white/10 border border-white/20 shadow-lg backdrop-blur-sm"
                    : "text-mono-text-muted hover:text-mono-text hover:bg-white/5 hover:backdrop-blur-sm hover:border hover:border-white/10 hover:shadow-md"
                )}
              >
                <span>{item.name}</span>
              </ScrollLink>
            </li>
          ))}
        </ul>
      </div>
    );
  }

  return (
    <div className="absolute inset-0 m-auto hidden size-fit sm-tablet:block">
      <ul className="flex gap-2 text-xs sm-tablet:gap-3 sm-tablet:text-sm tablet:gap-6 laptop-sm:gap-8">
        {menuItems.map((item, index) => (
          <li key={index}>
            <ScrollLink
              to={item.href}
              className={cn(
                "block duration-300 font-medium transition-all px-2 py-1.5 rounded-lg relative group hover:scale-105 sm-tablet:px-3 sm-tablet:py-2 sm-tablet:rounded-xl tablet:px-4 tablet:py-2.5 min-h-[36px] flex items-center justify-center",
                location.pathname === item.href
                  ? "text-mono-text bg-white/10 border border-white/20 shadow-lg backdrop-blur-sm"
                  : "text-mono-text-muted hover:text-mono-text hover:bg-white/5 hover:backdrop-blur-sm hover:border hover:border-white/10 hover:shadow-md"
              )}
            >
              <span className="relative z-10 whitespace-nowrap">{item.name}</span>
            </ScrollLink>
          </li>
        ))}
      </ul>
    </div>
  );
}
