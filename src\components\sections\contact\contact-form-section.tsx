'use client'
import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Send, CheckCircle, User, Mail, MessageSquare, Briefcase, AlertCircle, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ToastContainer, useToast } from '@/components/ui/toast'
import { sendEmail, isValidEmail, checkRateLimit, setRateLimit, initEmailJS, type EmailTemplateParams } from '@/lib/emailjs'

// Form data interface
interface FormData {
  name: string
  email: string
  subject: string
  message: string
  honeypot: string // Spam protection
}

// Form validation errors interface
interface FormErrors {
  name?: string
  email?: string
  subject?: string
  message?: string
  general?: string
}

// Project types for the dropdown - Updated for frontend focus
const projectTypes = [
  'React Web Application',
  'Business Website',
  'Portfolio Website',
  'Landing Page',
  'E-commerce Frontend',
  'Component Library',
  'UI/UX Implementation',
  'Website Redesign',
  'Other'
]

// Services I can help with - Updated for junior frontend developer
const services = [
  'React websites & components',
  'Mobile-friendly designs',
  'Clean CSS & Tailwind styling',
  'Interactive user interfaces',
  'Fast loading websites',
  'Design to code conversion'
]

export function ContactFormSection() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    subject: '',
    message: '',
    honeypot: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [errors, setErrors] = useState<FormErrors>({})
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const { toasts, removeToast, showSuccess, showError, showWarning } = useToast()

  // Initialize EmailJS on component mount
  useEffect(() => {
    initEmailJS()
  }, [])

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Check honeypot (spam protection)
    if (formData.honeypot) {
      newErrors.general = 'Spam detected'
      setErrors(newErrors)
      return false
    }

    // Validate required fields
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!isValidEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Message is required'
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'Message must be at least 10 characters long'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!validateForm()) {
      showError('Validation Error', 'Please fix the errors below and try again.')
      return
    }

    // Check rate limiting
    if (!checkRateLimit()) {
      showWarning('Please Wait', 'You can only send one message per minute. Please wait before sending another.')
      return
    }

    setIsSubmitting(true)
    setErrors({})

    try {
      // Prepare email template parameters
      const templateParams: EmailTemplateParams = {
        from_name: formData.name.trim(),
        from_email: formData.email.trim(),
        project_type: formData.subject || 'General Inquiry',
        message: formData.message.trim(),
        to_name: 'Christian Jerald Jutba',
        reply_to: formData.email.trim(),
      }

      // Send email via EmailJS
      const success = await sendEmail(templateParams)

      if (success) {
        // Set rate limit
        setRateLimit()

        // Show success state
        setIsSubmitted(true)
        showSuccess('Message Sent!', 'Thanks for reaching out! I\'ll get back to you within 24 hours.')

        // Reset form
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: '',
          honeypot: ''
        })

        // Reset success state after 5 seconds
        setTimeout(() => setIsSubmitted(false), 5000)
      } else {
        throw new Error('Failed to send email')
      }
    } catch (error) {
      console.error('Form submission error:', error)
      showError('Failed to Send', 'Something went wrong. Please try again or contact me <NAME_EMAIL>')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <section id="contact-form" className="relative bg-mono-bg py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32">
      <div className="max-w-7xl mx-auto px-4 max-[424px]:px-8 mobile:px-8"> {/* Match beyond-the-code section content width */}
        <div className="grid lg:grid-cols-2 gap-8 max-[424px]:gap-6 mobile:gap-6 sm:gap-8 md:gap-10 lg:gap-12">
          {/* Left column - Information */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6 max-[424px]:space-y-4 mobile:space-y-4 sm:space-y-6 md:space-y-6 lg:space-y-8 hidden lg:block"
          >
            <div>
              <motion.h2
                className="text-3xl max-[424px]:text-3xl mobile:text-3xl sm:text-2xl md:text-4xl lg:text-4xl font-bold text-mono-text mb-4 max-[424px]:mb-3 mobile:mb-3 sm:mb-4 md:mb-5 lg:mb-6 leading-tight tracking-tight"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                viewport={{ once: true }}
              >
                Ready to Start Your{' '}
                <span className="relative inline-block">
                  <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                    Project?
                  </span>
                  <span
                    className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-15"
                    aria-hidden="true"
                  />
                </span>
              </motion.h2>

              <motion.p
                className="text-base max-[424px]:text-sm mobile:text-sm sm:text-base md:text-base lg:text-lg text-mono-secondary leading-relaxed mb-6 max-[424px]:mb-4 mobile:mb-4 sm:mb-5 md:mb-6 lg:mb-8"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
              >
                I enjoy creating websites with React and learning something new every day.
                Need help with your project? Let's talk about it!
              </motion.p>
            </div>
            
            {/* What I can help with */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              viewport={{ once: true }}
            >
              <h3 className="text-mono-text text-sm max-[424px]:text-xs mobile:text-xs sm:text-sm md:text-sm lg:text-base font-semibold mb-3 max-[424px]:mb-2 mobile:mb-2 sm:mb-3 md:mb-3 lg:mb-4 flex items-center gap-2">
                <Briefcase className="w-4 h-4 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-4 sm:h-4 md:w-4 md:h-4 lg:w-5 lg:h-5 text-accent-electric" />
                What I can help with:
              </h3>
              <div className="grid grid-cols-1 gap-2 max-[424px]:gap-1.5 mobile:gap-1.5 sm:gap-2.5 md:gap-2.5 lg:gap-3">
                {services.map((service, index) => (
                  <motion.div
                    key={service}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-center gap-2.5 max-[424px]:gap-2 mobile:gap-2 sm:gap-2.5 md:gap-2.5 lg:gap-3 text-mono-secondary text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm"
                  >
                    <div className="w-1.5 h-1.5 max-[424px]:w-1 max-[424px]:h-1 mobile:w-1 mobile:h-1 sm:w-1.5 sm:h-1.5 md:w-1.5 md:h-1.5 lg:w-2 lg:h-2 bg-accent-neon rounded-full flex-shrink-0" />
                    {service}
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Response time info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              viewport={{ once: true }}
              className="bg-mono-surface/[0.08] border border-mono-border rounded-xl max-[424px]:rounded-lg mobile:rounded-lg sm:rounded-xl md:rounded-xl lg:rounded-2xl p-4 max-[424px]:p-3 mobile:p-3 sm:p-4 md:p-5 lg:p-6 backdrop-blur-xl"
            >
              <h3 className="text-mono-text text-base max-[424px]:text-sm mobile:text-sm sm:text-sm md:text-sm lg:text-base font-semibold mb-3 max-[424px]:mb-2.5 mobile:mb-2.5 sm:mb-2.5 md:mb-2.5 lg:mb-3 flex items-center gap-2">
                <CheckCircle className="w-5 h-5 max-[424px]:w-4 max-[424px]:h-4 mobile:w-4 mobile:h-4 sm:w-4 sm:h-4 md:w-4 md:h-4 lg:w-5 lg:h-5 text-accent-electric" />
                Response Time
              </h3>
              <p className="text-mono-secondary text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm">
                I reply within 24 hours. Looking forward to hearing about your project!
              </p>
            </motion.div>
          </motion.div>

          {/* Right column - Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="bg-mono-surface/[0.08] border border-mono-border rounded-xl max-[424px]:rounded-lg mobile:rounded-lg sm:rounded-xl md:rounded-xl lg:rounded-2xl p-6 max-[424px]:p-4 mobile:p-4 sm:p-5 md:p-6 lg:p-8 backdrop-blur-xl">
              {isSubmitted ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center py-8 max-[424px]:py-6 mobile:py-6"
                >
                  <div className="w-12 h-12 max-[424px]:w-10 max-[424px]:h-10 mobile:w-10 mobile:h-10 bg-accent-electric/20 rounded-full flex items-center justify-center mx-auto mb-3 max-[424px]:mb-2 mobile:mb-2">
                    <CheckCircle className="w-6 h-6 max-[424px]:w-5 max-[424px]:h-5 mobile:w-5 mobile:h-5 text-accent-electric" />
                  </div>
                  <h3 className="text-xl max-[424px]:text-lg mobile:text-lg sm:text-xl md:text-xl lg:text-2xl font-bold text-mono-text mb-2 max-[424px]:mb-1.5 mobile:mb-1.5 sm:mb-1.5 md:mb-1.5 lg:mb-2">Message Sent!</h3>
                  <p className="text-mono-secondary text-sm max-[424px]:text-xs mobile:text-xs sm:text-sm md:text-sm lg:text-base">
                    Thanks for reaching out! I'll get back to you within 24 hours.
                  </p>
                </motion.div>
              ) : (
                <>
                  <h2 className="text-xl max-[424px]:text-lg mobile:text-lg sm:text-xl md:text-xl lg:text-2xl font-bold text-mono-text mb-4 max-[424px]:mb-3 mobile:mb-3 sm:mb-4 md:mb-5 lg:mb-6">Send me a message</h2>
                  <form onSubmit={handleSubmit} className="space-y-4 max-[424px]:space-y-3 mobile:space-y-3 sm:space-y-4 md:space-y-5 lg:space-y-6">
                    {/* Honeypot field for spam protection - hidden from users */}
                    <input
                      type="text"
                      name="honeypot"
                      value={formData.honeypot}
                      onChange={handleChange}
                      style={{ display: 'none' }}
                      tabIndex={-1}
                      autoComplete="off"
                    />

                    {/* Name and Email row */}
                    <div className="grid max-[424px]:grid-cols-1 mobile:grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-4 max-[424px]:gap-3 mobile:gap-3 sm:gap-4 md:gap-5 lg:gap-6">
                      <div>
                        <label htmlFor="name" className="block text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm font-medium text-mono-text mb-1.5 max-[424px]:mb-1 mobile:mb-1 sm:mb-1.5 md:mb-1.5 lg:mb-2 flex items-center gap-2">
                          <User className="w-3.5 h-3.5 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4 text-accent-electric" />
                          Name *
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          required
                          className={`w-full px-3 py-2.5 max-[424px]:px-3 max-[424px]:py-2 mobile:px-3 mobile:py-2 sm:px-3 sm:py-2.5 md:px-3 md:py-2.5 lg:px-4 lg:py-3 bg-mono-surface/30 border rounded-lg max-[424px]:rounded-md mobile:rounded-md sm:rounded-lg md:rounded-lg lg:rounded-xl text-mono-text text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm placeholder-mono-secondary focus:outline-none focus:ring-2 transition-all duration-300 ${
                            errors.name
                              ? 'border-red-500/50 focus:border-red-500/50 focus:ring-red-500/20'
                              : 'border-mono-border focus:border-accent-electric/50 focus:ring-accent-electric/20'
                          }`}
                          placeholder="Your name"
                        />
                        {errors.name && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="text-red-400 text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm mt-1 max-[424px]:mt-0.5 mobile:mt-0.5 sm:mt-0.5 md:mt-0.5 lg:mt-1 flex items-center gap-1"
                          >
                            <AlertCircle className="w-2.5 h-2.5 max-[424px]:w-2 max-[424px]:h-2 mobile:w-2 mobile:h-2 sm:w-2.5 sm:h-2.5 md:w-2.5 md:h-2.5 lg:w-3 lg:h-3" />
                            {errors.name}
                          </motion.p>
                        )}
                      </div>
                      <div>
                        <label htmlFor="email" className="block text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm font-medium text-mono-text mb-1.5 max-[424px]:mb-1 mobile:mb-1 sm:mb-1.5 md:mb-1.5 lg:mb-2 flex items-center gap-2">
                          <Mail className="w-3.5 h-3.5 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4 text-accent-electric" />
                          Email *
                        </label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          required
                          className={`w-full px-3 py-2.5 max-[424px]:px-3 max-[424px]:py-2 mobile:px-3 mobile:py-2 sm:px-3 sm:py-2.5 md:px-3 md:py-2.5 lg:px-4 lg:py-3 bg-mono-surface/30 border rounded-lg max-[424px]:rounded-md mobile:rounded-md sm:rounded-lg md:rounded-lg lg:rounded-xl text-mono-text text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm placeholder-mono-secondary focus:outline-none focus:ring-2 transition-all duration-300 ${
                            errors.email
                              ? 'border-red-500/50 focus:border-red-500/50 focus:ring-red-500/20'
                              : 'border-mono-border focus:border-accent-electric/50 focus:ring-accent-electric/20'
                          }`}
                          placeholder="<EMAIL>"
                        />
                        {errors.email && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="text-red-400 text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm mt-1 max-[424px]:mt-0.5 mobile:mt-0.5 sm:mt-0.5 md:mt-0.5 lg:mt-1 flex items-center gap-1"
                          >
                            <AlertCircle className="w-2.5 h-2.5 max-[424px]:w-2 max-[424px]:h-2 mobile:w-2 mobile:h-2 sm:w-2.5 sm:h-2.5 md:w-2.5 md:h-2.5 lg:w-3 lg:h-3" />
                            {errors.email}
                          </motion.p>
                        )}
                      </div>
                    </div>

                    {/* Project Type */}
                    <div className="relative">
                      <label htmlFor="subject" className="block text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm font-medium text-mono-text mb-1.5 max-[424px]:mb-1 mobile:mb-1 sm:mb-1.5 md:mb-1.5 lg:mb-2 flex items-center gap-2">
                        <Briefcase className="w-3.5 h-3.5 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4 text-accent-electric" />
                        Project Type
                      </label>
                      <div className="relative">
                        <button
                          type="button"
                          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                          className="w-full px-3 py-2.5 max-[424px]:px-3 max-[424px]:py-2 mobile:px-3 mobile:py-2 sm:px-3 sm:py-2.5 md:px-3 md:py-2.5 lg:px-4 lg:py-3 bg-mono-surface/30 border border-mono-border rounded-lg max-[424px]:rounded-md mobile:rounded-md sm:rounded-lg md:rounded-lg lg:rounded-xl text-mono-text text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm focus:outline-none focus:border-accent-electric/50 focus:ring-2 focus:ring-accent-electric/20 transition-all duration-300 cursor-pointer text-left flex items-center justify-between"
                        >
                          <span className={formData.subject ? 'text-mono-text' : 'text-mono-text/60'}>
                            {formData.subject || 'Select a project type'}
                          </span>
                          <ChevronDown className={`w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-4 sm:h-4 md:w-4 md:h-4 lg:w-5 lg:h-5 text-mono-text/60 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`} />
                        </button>

                        {isDropdownOpen && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ duration: 0.2 }}
                            className="absolute top-full left-0 right-0 mt-1 bg-mono-surface border border-mono-border rounded-lg max-[424px]:rounded-md mobile:rounded-md md:rounded-lg lg:rounded-xl shadow-lg z-50 max-h-60 overflow-y-auto"
                          >
                            {projectTypes.map((type) => (
                              <button
                                key={type}
                                type="button"
                                onClick={() => {
                                  setFormData(prev => ({ ...prev, subject: type }))
                                  setIsDropdownOpen(false)
                                  if (errors.subject) {
                                    setErrors(prev => ({ ...prev, subject: undefined }))
                                  }
                                }}
                                className="w-full px-3 py-2.5 max-[424px]:px-3 max-[424px]:py-2 mobile:px-3 mobile:py-2 sm:px-3 sm:py-2.5 md:px-3 md:py-2.5 lg:px-4 lg:py-3 text-left text-mono-text text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm hover:bg-mono-surface/50 transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg max-[424px]:first:rounded-t-md max-[424px]:last:rounded-b-md mobile:first:rounded-t-md mobile:last:rounded-b-md"
                              >
                                {type}
                              </button>
                            ))}
                          </motion.div>
                        )}
                      </div>
                      {errors.subject && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-1 text-sm text-red-400 flex items-center gap-1"
                        >
                          <AlertCircle className="w-4 h-4" />
                          {errors.subject}
                        </motion.p>
                      )}
                    </div>

                    {/* Message */}
                    <div>
                      <label htmlFor="message" className="block text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm font-medium text-mono-text mb-1.5 max-[424px]:mb-1 mobile:mb-1 sm:mb-1.5 md:mb-1.5 lg:mb-2 flex items-center gap-2">
                        <MessageSquare className="w-3.5 h-3.5 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4 text-accent-electric" />
                        Message *
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleChange}
                        required
                        rows={5}
                        className={`w-full px-3 py-2.5 max-[424px]:px-3 max-[424px]:py-2 mobile:px-3 mobile:py-2 sm:px-3 sm:py-2.5 md:px-3 md:py-2.5 lg:px-4 lg:py-3 bg-mono-surface/30 border rounded-lg max-[424px]:rounded-md mobile:rounded-md sm:rounded-lg md:rounded-lg lg:rounded-xl text-mono-text text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm placeholder-mono-secondary focus:outline-none focus:ring-2 transition-all duration-300 resize-vertical ${

                          errors.message
                            ? 'border-red-500/50 focus:border-red-500/50 focus:ring-red-500/20'
                            : 'border-mono-border focus:border-accent-electric/50 focus:ring-accent-electric/20'
                        }`}
                        placeholder="Tell me about your project, what you're looking to build, and how I can help..."
                      />
                      {errors.message && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="text-red-400 text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm mt-1 max-[424px]:mt-0.5 mobile:mt-0.5 sm:mt-0.5 md:mt-0.5 lg:mt-1 flex items-center gap-1"
                        >
                          <AlertCircle className="w-2.5 h-2.5 max-[424px]:w-2 max-[424px]:h-2 mobile:w-2 mobile:h-2" />
                          {errors.message}
                        </motion.p>
                      )}
                    </div>

                    {/* Submit button */}
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full bg-accent-electric text-black font-semibold hover:bg-blue-500 hover:shadow-lg hover:shadow-accent-electric/25 transition-all duration-300 px-6 py-2.5 max-[424px]:px-4 max-[424px]:py-2 mobile:px-4 mobile:py-2 sm:px-6 sm:py-2.5 md:px-6 md:py-2.5 lg:px-8 lg:py-3 h-auto disabled:opacity-50 disabled:cursor-not-allowed text-sm max-[424px]:text-xs mobile:text-xs rounded-lg max-[424px]:rounded-md mobile:rounded-md"
                      >
                        {isSubmitting ? (
                          <div className="flex items-center gap-2 max-[424px]:gap-1.5 mobile:gap-1.5">
                            <div className="w-3.5 h-3.5 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 border-2 border-black/30 border-t-black rounded-full animate-spin" />
                            <span className="max-[424px]:hidden mobile:hidden sm:inline">Sending Message...</span>
                            <span className="max-[424px]:inline mobile:inline sm:hidden">Sending...</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2 max-[424px]:gap-1.5 mobile:gap-1.5">
                            <Send className="w-3.5 h-3.5 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />
                            Send Message
                          </div>
                        )}
                      </Button>
                    </motion.div>
                  </form>
                </>
              )}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Toast notifications */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </section>
  )
}
