'use client'
import React from 'react'
import { motion } from 'framer-motion'
import { ArrowRight, Download } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { AnimatedGroup } from '@/components/ui/animated-group'
import { ScrollIndicator } from '@/components/ui/scroll-indicator'

// Advanced animation variants
const transitionVariants = {
    item: {
        hidden: {
            opacity: 0,
            filter: 'blur(12px)',
            y: 12,
        },
        visible: {
            opacity: 1,
            filter: 'blur(0px)',
            y: 0,
            transition: {
                type: 'spring' as const,
                bounce: 0.3,
                duration: 1.5,
            },
        },
    },
}

// Enhanced text reveal variants
const textRevealVariants = {
    hidden: { opacity: 0, y: 20, rotateX: -90 },
    visible: (i: number) => ({
        opacity: 1,
        y: 0,
        rotateX: 0,
        transition: {
            delay: i * 0.05,
            duration: 0.6,
            type: "spring" as const,
            stiffness: 100,
            damping: 12
        }
    })
}

const getTypingDelay = (char: string): number => {
    const baseDelay = 80
    const variations = {
        ' ': baseDelay * 0.5,
        ',': baseDelay * 1.5,
        '.': baseDelay * 2,
        '!': baseDelay * 1.8,
        '?': baseDelay * 1.8,
    }

    const humanVariation = (Math.random() - 0.5) * 30
    const charDelay = variations[char as keyof typeof variations] || baseDelay

    return Math.max(30, charDelay + humanVariation)
}

export function HeroContent() {
    const navigate = useNavigate()
    const containerRef = React.useRef<HTMLDivElement>(null)
    const [showGradient, setShowGradient] = React.useState(false)
    const [typewriterText, setTypewriterText] = React.useState('')
    const [showCursor, setShowCursor] = React.useState(true)

    // Enhanced typewriter effect with realistic timing
    React.useEffect(() => {
        const timer = setTimeout(() => {
            setShowGradient(true)
            const text = 'user experiences'
            let index = 0

            const typeNextChar = () => {
                if (index < text.length) {
                    setTypewriterText(text.slice(0, index + 1))
                    const delay = getTypingDelay(text[index])
                    index++
                    setTimeout(typeNextChar, delay)
                } else {
                    setTimeout(() => setShowCursor(false), 500)
                }
            }

            typeNextChar()
        }, 1500)

        return () => clearTimeout(timer)
    }, [])

    const downloadResume = () => {
        // Replace with your actual resume file path
        const resumeUrl = '/resume.pdf' // You'll need to add your resume file to the public folder
        const link = document.createElement('a')
        link.href = resumeUrl
        link.download = 'CJ_Jutba_Resume.pdf'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    }

    const navigateToProjects = () => {
        navigate('/projects')
    }

    return (
        <div
            ref={containerRef}
            className="relative flex items-center justify-center px-4 max-[424px]:px-3 mobile:px-6 py-20 max-[424px]:py-8 min-[425px]:max-[639px]:py-8 sm:py-20 md:py-24 lg:py-32 overflow-hidden min-h-screen max-[424px]:min-h-[85vh] min-[425px]:max-[639px]:min-h-[85vh]"
            style={{ backgroundColor: 'rgb(var(--mono-bg))' }}
        >
            {/* Clean background with --mono-bg color - removed slanting lines, geometric shapes, and particles */}






            {/* Scroll indicator - Layer 50 */}
            <div className="absolute bottom-4 max-[424px]:bottom-5 mobile:bottom-5 left-1/2 transform -translate-x-1/2 z-20">
                <ScrollIndicator delay={3} />
            </div>

            {/* Main content - Layer 10 */}
            <div className="relative z-[10] mx-auto max-w-4xl text-center w-full max-[424px]:max-w-[85%] max-[424px]:px-2 max-[424px]:transform max-[424px]:-translate-y-8 min-[425px]:max-[639px]:transform min-[425px]:max-[639px]:-translate-y-8 sm">
                <AnimatedGroup
                    variants={{
                        container: {
                            visible: {
                                transition: {
                                    staggerChildren: 0.05,
                                    delayChildren: 0.75,
                                },
                            },
                        },
                        ...transitionVariants,
                    }}
                >
                    {/* Compressed status badge for mobile optimization */}
                    <motion.div
                        className="mb-4 max-[424px]:mb-4 mobile:mb-4 sm-tablet:mb-6 tablet:mb-8"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.5, duration: 0.6, type: "spring" }}
                    >
                        <motion.span
                            className="inline-flex items-center gap-2 max-[424px]:gap-2.5 mobile:gap-2.5 px-3 py-1.5 max-[424px]:px-4 max-[424px]:py-2 mobile:px-4 mobile:py-2 bg-gradient-to-r from-mono-surface/80 to-mono-surface-light/80 border border-mono-accent/30 rounded-full text-mono-accent text-xs max-[424px]:text-[11px] mobile:text-xs font-semibold backdrop-blur-md shadow-lg shadow-mono-accent/10"
                            whileHover={{
                                scale: 1.05,
                                borderColor: 'rgb(170, 170, 170)',
                                boxShadow: '0 0 30px rgba(170, 170, 170, 0.2)'
                            }}
                            animate={{
                                boxShadow: [
                                    '0 0 20px rgba(170, 170, 170, 0.1)',
                                    '0 0 30px rgba(170, 170, 170, 0.2)',
                                    '0 0 20px rgba(170, 170, 170, 0.1)'
                                ]
                            }}
                            transition={{
                                boxShadow: { duration: 3, repeat: Infinity, ease: [0.4, 0, 0.6, 1] as const }
                            }}
                        >
                            <motion.div
                                className="relative flex items-center justify-center"
                                animate={{
                                    scale: [1, 1.1, 1],
                                }}
                                transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: [0.4, 0, 0.6, 1] as const
                                }}
                            >
                                {/* Green pulsing dot */}
                                <motion.div
                                    className="w-2 h-2 max-[424px]:w-2.5 max-[424px]:h-2.5 mobile:w-2.5 mobile:h-2.5 bg-green-500 rounded-full"
                                    animate={{
                                        scale: [1, 1.3, 1],
                                        opacity: [0.8, 1, 0.8],
                                        boxShadow: [
                                            '0 0 0 0 rgba(34, 197, 94, 0.4)',
                                            '0 0 0 8px rgba(34, 197, 94, 0)',
                                            '0 0 0 0 rgba(34, 197, 94, 0)'
                                        ]
                                    }}
                                    transition={{
                                        duration: 1.5,
                                        repeat: Infinity,
                                        ease: [0.4, 0, 0.6, 1] as const
                                    }}
                                />
                                {/* Outer glow ring */}
                                <motion.div
                                    className="absolute inset-0 w-2 h-2 max-[424px]:w-2.5 max-[424px]:h-2.5 mobile:w-2.5 mobile:h-2.5 bg-green-400/30 rounded-full blur-sm"
                                    animate={{
                                        scale: [1, 1.5, 1],
                                        opacity: [0.6, 0.2, 0.6]
                                    }}
                                    transition={{
                                        duration: 2,
                                        repeat: Infinity,
                                        ease: [0.4, 0, 0.6, 1] as const
                                    }}
                                />
                            </motion.div>
                            <span className="relative">
                                Available for Work
                                <motion.div
                                    className="absolute -bottom-1 left-0 h-0.5 bg-gradient-to-r from-mono-accent to-transparent"
                                    initial={{ width: 0 }}
                                    animate={{ width: '100%' }}
                                    transition={{ delay: 1.5, duration: 1 }}
                                />
                            </span>
                        </motion.span>
                    </motion.div>

                    {/* Responsive typography with mobile optimization for <425px and 425px-639px */}
                    <div className="mb-3 max-[424px]:mb-3 mobile:mb-3 sm-tablet:mb-6 tablet:mb-8">
                        <h1 className="text-2xl max-[424px]:text-[1.5rem] mobile:text-3xl sm-tablet:text-[2rem] tablet:text-4xl laptop-sm:text-5xl font-bold text-mono-text leading-tight max-[424px]:leading-tight mobile:leading-tight sm-tablet:leading-tight tablet:leading-snug">
                            {/* Mobile layout: 3 lines for <640px */}
                            <div className="block sm-tablet:hidden space-y-0.5 max-[424px]:space-y-0.5 mobile:space-y-1">
                                {/* First line with word-by-word reveal for proper word breaking */}
                                <div className="block overflow-hidden break-words hyphens-none">
                                    <div className="max-[424px]:whitespace-normal max-[424px]:break-words max-[424px]:hyphens-none mobile:whitespace-normal mobile:break-words mobile:hyphens-none">
                                        {/* For <425px: Use word-based animation to prevent letter breaking */}
                                        <div className="max-[424px]:block hidden">
                                            {"I build modern websites".split(" ").map((word, i) => (
                                                <motion.span
                                                    key={i}
                                                    className="inline-block mr-1"
                                                    variants={textRevealVariants}
                                                    initial="hidden"
                                                    animate="visible"
                                                    custom={i * 3}
                                                >
                                                    {word}
                                                </motion.span>
                                            ))}
                                        </div>
                                        {/* For 425px-639px: Use character-based animation */}
                                        <div className="max-[424px]:hidden mobile:block hidden sm-tablet:hidden">
                                            {"I build modern websites".split("").map((char, i) => (
                                                <motion.span
                                                    key={i}
                                                    className="inline-block"
                                                    variants={textRevealVariants}
                                                    initial="hidden"
                                                    animate="visible"
                                                    custom={i}
                                                >
                                                    {char === " " ? "\u00A0" : char}
                                                </motion.span>
                                            ))}
                                        </div>
                                    </div>
                                </div>

                                {/* Second line - "that deliver exceptional" with word break control */}
                                <div className="block break-words hyphens-none">
                                    <span className="text-mono-text text-2xl max-[424px]:text-[1.5rem] mobile:text-3xl whitespace-nowrap max-[424px]:whitespace-normal max-[424px]:break-words max-[424px]:hyphens-none mobile:whitespace-normal mobile:break-words mobile:hyphens-none">that deliver exceptional</span>
                                </div>

                                {/* Third line with enhanced typewriter - "user experiences" */}
                                <div className="block break-words hyphens-none">
                                    {!showGradient && (
                                        <motion.span
                                            className="inline-block w-1 h-[0.9em] bg-mono-accent"
                                            animate={{
                                                opacity: [1, 0, 1],
                                                scaleY: [1, 0.8, 1]
                                            }}
                                            transition={{
                                                duration: 1.2,
                                                repeat: Infinity,
                                                ease: [0.4, 0, 0.6, 1] as const
                                            }}
                                        />
                                    )}
                                    {showGradient && (
                                        <span className="relative">
                                            <motion.span
                                                className="text-transparent bg-clip-text bg-gradient-to-r from-mono-accent via-mono-accent-light to-mono-text italic font-cursive text-2xl max-[424px]:text-[1.5rem] mobile:text-3xl whitespace-nowrap max-[424px]:whitespace-normal max-[424px]:break-words max-[424px]:hyphens-none mobile:whitespace-normal mobile:break-words mobile:hyphens-none"
                                                initial={{ backgroundPosition: "0% 50%" }}
                                                animate={{ backgroundPosition: "100% 50%" }}
                                                transition={{
                                                    duration: 3,
                                                    repeat: Infinity,
                                                    repeatType: "reverse",
                                                    ease: [0.4, 0, 0.6, 1] as const
                                                }}
                                                style={{
                                                    backgroundSize: "200% 200%"
                                                }}
                                            >
                                                {typewriterText}
                                            </motion.span>
                                            {showCursor && (
                                                <motion.span
                                                    className="text-mono-accent ml-1 text-lg max-[424px]:text-sm mobile:text-xl"
                                                    animate={{
                                                        opacity: [1, 0, 1],
                                                        scale: [1, 1.1, 1]
                                                    }}
                                                    transition={{
                                                        duration: 1,
                                                        repeat: Infinity,
                                                        ease: [0.4, 0, 0.6, 1] as const
                                                    }}
                                                >
                                                    |
                                                </motion.span>
                                            )}
                                        </span>
                                    )}
                                </div>
                            </div>

                            {/* Desktop layout: 2 lines for ≥640px */}
                            <div className="hidden sm-tablet:block space-y-1 tablet:space-y-2">
                                {/* First line with character-by-character reveal */}
                                <div className="block overflow-hidden">
                                    {"I build modern websites".split("").map((char, i) => (
                                        <motion.span
                                            key={i}
                                            className="inline-block"
                                            variants={textRevealVariants}
                                            initial="hidden"
                                            animate="visible"
                                            custom={i}
                                        >
                                            {char === " " ? "\u00A0" : char}
                                        </motion.span>
                                    ))}
                                </div>

                                {/* Second line - "that deliver exceptional user experiences" */}
                                <div className="block">
                                    <span className="text-mono-text sm-tablet:text-[2rem] tablet:text-4xl laptop-sm:text-5xl">that deliver exceptional </span>
                                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-mono-accent via-mono-accent-light to-mono-text italic font-cursive sm-tablet:text-[2rem] tablet:text-4xl laptop-sm:text-5xl">
                                        user experiences
                                    </span>
                                </div>
                            </div>
                        </h1>
                    </div>

                    {/* Profile section optimized for mobile - single row layout */}
                    <motion.div
                        className="mx-auto mt-2 max-[424px]:mt-2 mobile:mt-2 sm-tablet:mt-5 tablet:mt-6 max-w-2xl text-pretty text-base max-[424px]:text-base mobile:text-lg sm-tablet:text-lg tablet:text-lg laptop-sm:text-xl text-mono-secondary leading-relaxed max-[424px]:leading-relaxed mobile:leading-relaxed sm-tablet:leading-relaxed tablet:leading-normal flex items-center justify-center gap-2 max-[424px]:gap-3 mobile:gap-3 font-medium"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 2, duration: 0.8 }}
                    >
                        <motion.span
                            className="font-bold text-mono-text text-xs max-[424px]:text-xs mobile:text-sm sm-tablet:text-lg tablet:text-lg laptop-sm:text-xl"
                            whileHover={{ scale: 1.05, color: 'rgb(187, 187, 187)' }}
                        >
                            Hello, I'm CJ Jutba
                        </motion.span>

                        <motion.div
                            className="relative group cursor-pointer"
                            whileHover={{ scale: 1.1 }}
                        >
                            <motion.div
                                className="relative w-10 h-10 max-[424px]:w-8 max-[424px]:h-8 mobile:w-8 mobile:h-8 sm-tablet:w-12 sm-tablet:h-12 tablet:w-12 tablet:h-12 laptop-sm:w-14 laptop-sm:h-14"
                                animate={{
                                    rotate: [0, 5, -5, 0],
                                }}
                                transition={{
                                    duration: 4,
                                    repeat: Infinity,
                                    ease: [0.4, 0, 0.6, 1] as const
                                }}
                            >
                                <img
                                    src="/profile.png"
                                    alt="CJ Jutba Profile"
                                    className="w-full h-full rounded-full border-2 border-mono-accent/30 object-cover shadow-lg shadow-mono-accent/20 transition-all duration-500 group-hover:border-mono-accent group-hover:shadow-mono-accent/40"
                                />

                                {/* Animated ring around profile */}
                                <motion.div
                                    className="absolute inset-0 rounded-full border border-mono-accent/50"
                                    animate={{
                                        scale: [1, 1.1, 1],
                                        opacity: [0.5, 0.8, 0.5],
                                    }}
                                    transition={{
                                        duration: 2,
                                        repeat: Infinity,
                                        ease: [0.4, 0, 0.6, 1] as const
                                    }}
                                />

                                {/* Glow effect */}
                                <motion.div
                                    className="absolute inset-0 rounded-full bg-mono-accent/20 blur-lg"
                                    animate={{
                                        scale: [0.8, 1.2, 0.8],
                                        opacity: [0.3, 0.6, 0.3],
                                    }}
                                    transition={{
                                        duration: 3,
                                        repeat: Infinity,
                                        ease: [0.4, 0, 0.6, 1] as const
                                    }}
                                />
                            </motion.div>

                            <motion.span
                                className="absolute -bottom-1 -left-1 text-lg max-[424px]:text-base sm-tablet:text-base tablet:text-base laptop-sm:text-lg"
                                animate={{
                                    rotate: [0, 20, -20, 0],
                                    scale: [1, 1.2, 1],
                                }}
                                transition={{
                                    duration: 2.5,
                                    repeat: Infinity,
                                    ease: [0.4, 0, 0.6, 1] as const
                                }}
                            >
                                👋
                            </motion.span>
                        </motion.div>

                        <motion.span
                            className="font-bold text-mono-text text-xs max-[424px]:text-xs mobile:text-sm sm-tablet:text-lg tablet:text-lg laptop-sm:text-xl"
                            whileHover={{ scale: 1.05, color: 'rgb(187, 187, 187)' }}
                        >
                            a Front-end Developer
                        </motion.span>
                    </motion.div>

                    {/* Buttons optimized for mobile - properly centered */}
                    <motion.div
                        className="flex flex-col gap-3 max-[424px]:gap-3 mobile:gap-3 sm-tablet:flex-row sm-tablet:gap-3 justify-center items-center mt-4 max-[424px]:mt-4 mobile:mt-4 sm-tablet:mt-8 tablet:mt-10 laptop-sm:mt-12 w-full px-4 max-[424px]:px-4 mobile:px-4 sm-tablet:px-0 sm-tablet:max-w-fit sm-tablet:mx-auto"
                        initial={{ opacity: 0, y: 40 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 2.5, duration: 0.8 }}
                    >
                        {/* Primary CTA with magnetic effect */}
                        <motion.button
                            onClick={navigateToProjects}
                            className="group relative bg-gradient-to-r from-mono-surface to-mono-surface-light text-mono-text font-semibold px-5 py-2 max-[424px]:px-5 max-[424px]:py-2 mobile:px-5 mobile:py-2 rounded-lg transition-all duration-300 border border-mono-accent/30 focus:outline-none focus:ring-2 focus:ring-mono-accent focus:ring-offset-2 focus:ring-offset-mono-bg overflow-hidden max-[424px]:w-full mobile:w-full sm-tablet:w-fit min-h-[40px] max-[424px]:min-h-[40px] mobile:min-h-[40px] flex items-center justify-center text-center max-[424px]:max-w-xs mobile:max-w-xs max-[424px]:mx-auto mobile:mx-auto"
                            whileHover={{
                                scale: 1.02,
                                borderColor: 'rgb(170, 170, 170)',
                                boxShadow: '0 0 30px rgba(170, 170, 170, 0.2)'
                            }}
                            whileTap={{ scale: 0.98 }}
                        >
                            <motion.div
                                className="absolute inset-0 bg-gradient-to-r from-mono-accent/10 to-mono-accent-light/10"
                                initial={{ x: '-100%' }}
                                whileHover={{ x: '0%' }}
                                transition={{ duration: 0.3 }}
                            />
                            <span className="relative z-10 flex items-center gap-2 text-sm max-[424px]:text-sm mobile:text-sm">
                                View My Work
                                <motion.div
                                    animate={{ x: [0, 3, 0] }}
                                    transition={{ duration: 1.5, repeat: Infinity }}
                                >
                                    <ArrowRight className="w-4 h-4 max-[424px]:w-4 max-[424px]:h-4 mobile:w-4 mobile:h-4" />
                                </motion.div>
                            </span>
                        </motion.button>

                        {/* Download Resume Ghost Button */}
                        <motion.button
                            onClick={downloadResume}
                            className="group relative text-mono-accent hover:text-mono-text px-5 py-2 max-[424px]:px-5 max-[424px]:py-2 mobile:px-5 mobile:py-2 rounded-lg transition-all duration-300 overflow-hidden border-2 border-mono-accent/40 hover:border-mono-accent bg-transparent hover:bg-mono-accent/10 max-[424px]:w-full mobile:w-full sm-tablet:w-fit min-h-[40px] max-[424px]:min-h-[40px] mobile:min-h-[40px] flex items-center justify-center text-center max-[424px]:max-w-xs mobile:max-w-xs max-[424px]:mx-auto mobile:mx-auto"
                            whileHover={{
                                scale: 1.02,
                                borderColor: 'rgba(170, 170, 170, 0.8)'
                            }}
                            whileTap={{ scale: 0.98 }}
                            aria-label="Download resume"
                        >
                            <span className="relative z-10 flex items-center gap-2 font-medium text-sm max-[424px]:text-sm mobile:text-sm">
                                <Download className="w-4 h-4 max-[424px]:w-4 max-[424px]:h-4 mobile:w-4 mobile:h-4 group-hover:scale-110 transition-transform duration-300" />
                                <span>Download Resume</span>
                            </span>

                            {/* Ripple effect on click */}
                            <motion.div
                                className="absolute inset-0 bg-mono-accent/20 rounded-lg"
                                initial={{ scale: 0, opacity: 0 }}
                                whileTap={{ scale: 1, opacity: [0, 0.3, 0] }}
                                transition={{ duration: 0.3 }}
                            />
                        </motion.button>
                    </motion.div>
                </AnimatedGroup>
            </div>
        </div>
    )
}
