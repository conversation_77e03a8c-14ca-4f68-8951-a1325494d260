// import React from 'react';
import { ScrollLink } from '@/components/ui/scroll-link';
import { cn } from '@/lib/utils';

interface LogoProps {
  className?: string;
}

export function Logo({ className }: LogoProps) {
  return (
    <ScrollLink
      to="/"
      aria-label="home"
      className={cn('flex items-center space-x-2 transition-all duration-300 hover:scale-105 group', className)}
    >
      <img
        src="/logo/website.png"
        alt="CJ Logo"
        className="w-6 h-6 object-contain transition-all duration-300 group-hover:brightness-110"
      />
    </ScrollLink>
  );
}
