import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { scrollToTopDelayed } from '@/lib/scroll-to-top';
import {
  Search,
  Home,
  User,
  Code,
  FolderOpen,
  Mail,
  FileText,
  MessageCircle,
  Github,
  Linkedin,
  Facebook,
  ArrowUpRight,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useCommandPalette } from '@/hooks/use-command-palette';

export function CommandPaletteModal() {
  const { isOpen, close } = useCommandPalette();
  return <CommandPalette isOpen={isOpen} onClose={close} />;
}

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
}

interface CommandItem {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  action: () => void;
  category: 'navigation' | 'actions' | 'social';
  shortcut?: string;
}

export function CommandPalette({ isOpen, onClose }: CommandPaletteProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const navigate = useNavigate();
  const location = useLocation();
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Helper function for navigation with scroll-to-top
  const navigateWithScroll = useCallback((path: string) => {
    navigate(path);
    scrollToTopDelayed(100);
    onClose();
  }, [navigate, onClose]);

  // Route mapping for navigation commands
  const routeMap: Record<string, string> = useMemo(() => ({
    'home': '/',
    'about': '/about',
    'skills': '/skills',
    'projects': '/projects',
    'contact': '/contact'
  }), []);

  // Helper function to determine if a navigation command is active
  const isCommandActive = useCallback((command: CommandItem): boolean => {
    if (command.category !== 'navigation') return false;

    const currentPath = location.pathname;
    const commandPath = routeMap[command.id];

    // Handle root path specifically
    if (commandPath === '/' && currentPath === '/') {
      return true;
    }

    // For other paths, ensure exact match
    return currentPath === commandPath;
  }, [location.pathname, routeMap]);

  const modalRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const selectedItemRef = useRef<HTMLButtonElement>(null);

  const commands: CommandItem[] = useMemo(() => [
    // Navigation
    {
      id: 'home',
      title: 'Home',
      description: 'Welcome to my forever work-in-progress!',
      icon: Home,
      action: () => navigateWithScroll('/'),
      category: 'navigation'
    },
    {
      id: 'about',
      title: 'About',
      description: 'Learn more about me!',
      icon: User,
      action: () => navigateWithScroll('/about'),
      category: 'navigation'
    },
    {
      id: 'skills',
      title: 'Skills',
      description: 'My technical expertise and capabilities',
      icon: Code,
      action: () => navigateWithScroll('/skills'),
      category: 'navigation'
    },
    {
      id: 'projects',
      title: 'Projects',
      description: 'Showcase of my projects',
      icon: FolderOpen,
      action: () => navigateWithScroll('/projects'),
      category: 'navigation'
    },
    {
      id: 'contact',
      title: 'Contact',
      description: 'Get in touch with me',
      icon: Mail,
      action: () => navigateWithScroll('/contact'),
      category: 'navigation'
    },
    // Quick Actions
    {
      id: 'resume',
      title: 'Resume',
      description: 'Download my latest resume',
      icon: FileText,
      action: () => { window.open('/public/resume.pdf', '_blank'); onClose(); },
      category: 'actions'
    },
    {
      id: 'lets-talk',
      title: "Let's Talk",
      description: 'Start a conversation with me',
      icon: MessageCircle,
      action: () => navigateWithScroll('/contact'),
      category: 'actions'
    },
    // Social Links
    {
      id: 'github',
      title: 'GitHub',
      description: 'Check out my code repositories',
      icon: Github,
      action: () => { window.open('https://github.com/christianjeraldjutba', '_blank'); onClose(); },
      category: 'social'
    },
    {
      id: 'linkedin',
      title: 'LinkedIn',
      description: 'Connect with me professionally',
      icon: Linkedin,
      action: () => { window.open('https://www.linkedin.com/in/christian-jerald-jutba-7a60b236a/', '_blank'); onClose(); },
      category: 'social'
    },
    {
      id: 'facebook',
      title: 'Facebook',
      description: 'Follow me on social media',
      icon: Facebook,
      action: () => { window.open('https://www.facebook.com/profile.php?id=61558829783116', '_blank'); onClose(); },
      category: 'social'
    }
  ], [navigateWithScroll, onClose]);

  const filteredCommands = commands.filter(command =>
    command.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    command.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const groupedCommands = {
    navigation: filteredCommands.filter(cmd => cmd.category === 'navigation'),
    actions: filteredCommands.filter(cmd => cmd.category === 'actions'),
    social: filteredCommands.filter(cmd => cmd.category === 'social')
  };

  // Auto-scroll to selected item
  useEffect(() => {
    if (selectedItemRef.current && contentRef.current) {
      selectedItemRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
      });
    }
  }, [selectedIndex]);

  // Keyboard navigation
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => {
            const maxIndex = filteredCommands.length - 1;
            return prev >= maxIndex ? 0 : prev + 1;
          });
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => {
            const maxIndex = filteredCommands.length - 1;
            return prev <= 0 ? maxIndex : prev - 1;
          });
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            filteredCommands[selectedIndex].action();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, filteredCommands, selectedIndex, onClose]);

  // Focus management - removed autofocus to prevent mobile keyboard popup
  // Users can manually click on the search bar when needed

  // Reset state when opening and set initial selection to active page
  useEffect(() => {
    if (isOpen) {
      setSearchQuery('');

      // Create a fresh filtered commands array for this effect
      const currentFilteredCommands = commands.filter(command =>
        command.title.toLowerCase().includes('') ||
        command.description.toLowerCase().includes('')
      );

      // Find the currently active navigation command and set it as selected
      const activeCommandIndex = currentFilteredCommands.findIndex(command =>
        command.category === 'navigation' && isCommandActive(command)
      );

      // If we found an active command, set it as selected, otherwise default to 0
      setSelectedIndex(activeCommandIndex >= 0 ? activeCommandIndex : 0);
    }
  }, [isOpen, location.pathname, commands, isCommandActive]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Reset selected index when search query changes
  useEffect(() => {
    if (isOpen && searchQuery) {
      // When searching, reset to first item
      setSelectedIndex(0);
    }
  }, [searchQuery, isOpen]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 bg-[#000000]/70 backdrop-blur-sm"
      >
        <div className="flex min-h-screen items-start justify-center p-4 pt-[10vh]">
          <motion.div
            ref={modalRef}
            initial={{ opacity: 0, scale: 0.95, y: -20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -20 }}
            className="w-full max-w-2xl bg-[#111111]/95 backdrop-blur-xl border border-[#333333]/50 rounded-2xl shadow-2xl overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center gap-3 p-4 border-b border-[#333333]/30">
              <Search className="w-5 h-5 text-[#888888]" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 bg-transparent text-[#E0E0E0] placeholder-[#666666] outline-none text-lg"
              />
              <button
                onClick={onClose}
                className="p-1 text-[#888888] hover:text-[#E0E0E0] transition-colors rounded-md hover:bg-[#222222]/50"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            {/* Content */}
            <div
              ref={contentRef}
              className="max-h-[60vh] overflow-y-auto scrollbar-thin"
            >
              {filteredCommands.length === 0 ? (
                <div className="p-8 text-center text-[#666666]">
                  No results found for "{searchQuery}"
                </div>
              ) : (
                <div className="p-2">
                  {/* Navigation Section */}
                  {groupedCommands.navigation.length > 0 && (
                    <div className="mb-4">
                      <div className="px-3 py-2 text-xs font-semibold text-[#666666] uppercase tracking-wider">
                        Navigation
                      </div>
                      {groupedCommands.navigation.map((command) => {
                        const globalIndex = filteredCommands.indexOf(command);
                        return (
                          <CommandItem
                            key={command.id}
                            command={command}
                            isSelected={globalIndex === selectedIndex}
                            isActive={isCommandActive(command)}
                            onClick={() => command.action()}
                            ref={globalIndex === selectedIndex ? selectedItemRef : null}
                          />
                        );
                      })}
                    </div>
                  )}

                  {/* Actions Section */}
                  {groupedCommands.actions.length > 0 && (
                    <div className="mb-4">
                      <div className="px-3 py-2 text-xs font-semibold text-[#666666] uppercase tracking-wider">
                        Quick Actions
                      </div>
                      {groupedCommands.actions.map((command) => {
                        const globalIndex = filteredCommands.indexOf(command);
                        return (
                          <CommandItem
                            key={command.id}
                            command={command}
                            isSelected={globalIndex === selectedIndex}
                            isActive={isCommandActive(command)}
                            onClick={() => command.action()}
                            ref={globalIndex === selectedIndex ? selectedItemRef : null}
                          />
                        );
                      })}
                    </div>
                  )}

                  {/* Social Section */}
                  {groupedCommands.social.length > 0 && (
                    <div className="mb-4">
                      <div className="px-3 py-2 text-xs font-semibold text-[#666666] uppercase tracking-wider">
                        Social Links
                      </div>
                      {groupedCommands.social.map((command) => {
                        const globalIndex = filteredCommands.indexOf(command);
                        return (
                          <CommandItem
                            key={command.id}
                            command={command}
                            isSelected={globalIndex === selectedIndex}
                            isActive={isCommandActive(command)}
                            onClick={() => command.action()}
                            ref={globalIndex === selectedIndex ? selectedItemRef : null}
                          />
                        );
                      })}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between px-4 py-3 bg-[#0A0A0A]/50 border-t border-[#333333]/30 text-xs text-[#666666]">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <kbd className="px-1.5 py-0.5 bg-[#111111]/70 rounded border border-[#333333]/30 text-xs">↑↓</kbd>
                  <span>navigate</span>
                </div>
                <div className="flex items-center gap-1">
                  <kbd className="px-1.5 py-0.5 bg-[#111111]/70 rounded border border-[#333333]/30 text-xs">⏎</kbd>
                  <span>select</span>
                </div>
                <div className="flex items-center gap-1">
                  <kbd className="px-1.5 py-0.5 bg-[#111111]/70 rounded border border-[#333333]/30 text-xs">esc</kbd>
                  <span>close</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

interface CommandItemProps {
  command: CommandItem;
  isSelected: boolean;
  isActive: boolean;
  onClick: () => void;
}

const CommandItem = React.forwardRef<HTMLButtonElement, CommandItemProps>(
  ({ command, isSelected, isActive, onClick }, ref) => {
    const Icon = command.icon;

    return (
      <button
        ref={ref}
        onClick={onClick}
        className={cn(
          'w-full flex items-center gap-3 px-3 py-3 rounded-lg text-left transition-all duration-200 border',
          // Active state (current page)
          isActive && 'bg-[#111111]/70 text-[#E0E0E0] border-[#333333]/30',
          // Selected state (keyboard navigation)
          isSelected && !isActive && 'bg-[#111111]/50 text-[#E0E0E0] border-[#333333]/20',
          // Default state
          !isSelected && !isActive && 'text-[#888888] border-transparent hover:bg-[#111111]/30 hover:text-[#E0E0E0] hover:border-[#333333]/20'
        )}
      >
        <div className={cn(
          'w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200',
          isActive
            ? 'bg-[#111111]/70 ring-1 ring-[#333333]/30'
            : isSelected
            ? 'bg-[#111111]/50'
            : 'bg-[#0A0A0A]/50'
        )}>
          <Icon className="w-4 h-4" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="font-medium truncate">{command.title}</div>
          <div className="text-xs text-[#666666] truncate">{command.description}</div>
        </div>
        {command.category === 'social' && (
          <ArrowUpRight className="w-4 h-4 text-[#666666]" />
        )}
      </button>
    );
  }
);