'use client'
import React from 'react'
import { motion } from 'framer-motion'
import { ArrowR<PERSON> } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { AnimatedGroup } from '@/components/ui/animated-group'
import { ScrollLink } from '@/components/ui/scroll-link'

// Problem data interface
interface Problem {
  title: string
  problemDescription: string
  solutionApproach: string
  technologies: string[]
  beforeAfter: {
    before: string
    after: string
  }
  color: string
  gradientColor: string
}

// Problem-solving data
const workflowProblems: Problem[] = [
  {
    title: 'Manual Data Entry',
    problemDescription: 'Manual data entry creates bottlenecks and errors',
    solutionApproach: 'Automated form processing and data validation',
    technologies: ['React Hook Form', 'Zod Validation', 'API Integration'],
    beforeAfter: {
      before: '2+ hours of manual entry',
      after: '5 minutes automated processing'
    },
    color: 'text-blue-400',
    gradientColor: 'from-blue-400 to-cyan-500'
  },
  {
    title: 'Disconnected Tools',
    problemDescription: 'Scattered tools create workflow friction',
    solutionApproach: 'Integrated dashboards and seamless workflows',
    technologies: ['API Integrations', 'Unified Interfaces', 'Real-time Sync'],
    beforeAfter: {
      before: '5+ different platforms',
      after: 'Single unified dashboard'
    },
    color: 'text-green-400',
    gradientColor: 'from-green-400 to-emerald-500'
  },
  {
    title: 'Repetitive Tasks',
    problemDescription: 'Repetitive tasks waste valuable time',
    solutionApproach: 'Smart automation and workflow optimization',
    technologies: ['Background Jobs', 'Scheduled Tasks', 'Smart Triggers'],
    beforeAfter: {
      before: 'Daily manual processes',
      after: 'Automated background tasks'
    },
    color: 'text-purple-400',
    gradientColor: 'from-purple-400 to-pink-500'
  }
]

// Animation variants following the established pattern
const transitionVariants = {
  item: {
    hidden: {
      opacity: 0,
      filter: 'blur(12px)',
      y: 12,
    },
    visible: {
      opacity: 1,
      filter: 'blur(0px)',
      y: 0,
      transition: {
        type: 'spring' as const,
        bounce: 0.3,
        duration: 1.5,
      },
    },
  },
}

// Technology badge component with enhanced hover effects
const TechBadge: React.FC<{ tech: string; color: string }> = ({ tech, color }) => (
  <span className={`px-2.5 py-1 bg-zinc-900/50 border border-zinc-700/60 rounded-full text-xs font-medium backdrop-blur-sm hover:bg-zinc-800/70 hover:border-cyan-400/50 hover:scale-105 transition-all duration-300 cursor-default ${color} hover:brightness-125`}>
    {tech}
  </span>
)

// Problem card component with variant support
const ProblemCard: React.FC<{
  problem: Problem;
  index: number;
  variant?: 'large' | 'medium' | 'compact' | 'mobile'
}> = ({ problem, index, variant = 'medium' }) => {

  // Variant-specific styling
  const getVariantStyles = () => {
    switch (variant) {
      case 'large':
        return {
          container: 'p-6',
          title: 'text-xl',
          description: 'text-sm',
          showFullContent: true
        }
      case 'medium':
        return {
          container: 'p-5',
          title: 'text-lg',
          description: 'text-sm',
          showFullContent: true
        }
      case 'compact':
        return {
          container: 'p-4',
          title: 'text-base',
          description: 'text-xs',
          showFullContent: false
        }
      case 'mobile':
        return {
          container: 'p-5',
          title: 'text-lg',
          description: 'text-sm',
          showFullContent: true
        }
      default:
        return {
          container: 'p-5',
          title: 'text-lg',
          description: 'text-sm',
          showFullContent: true
        }
    }
  }

  const styles = getVariantStyles()

  return (
    <motion.div
      initial={{ opacity: 0, y: 40 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: index * 0.15 }}
      className="group relative h-full"
    >
      <div className={`relative bg-zinc-900/40 border border-zinc-800/60 rounded-2xl ${styles.container} backdrop-blur-sm hover:bg-zinc-800/70 hover:border-cyan-400/50 transition-all duration-300 hover:scale-[1.03] hover:shadow-lg hover:shadow-cyan-400/25 h-full`}>

        {/* Problem Statement */}
        <div className="mb-4">
          <h3 className={`${styles.title} font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300`}>
            {problem.title}
          </h3>
          <p className={`text-slate-400 ${styles.description} leading-relaxed`}>
            {problem.problemDescription}
          </p>
        </div>

        {styles.showFullContent && (
          <>
            {/* Solution Approach */}
            <div className="mb-4">
              <span className="text-sm font-semibold text-cyan-400 mb-2 block">Solution</span>
              <p className={`text-slate-300 ${styles.description} leading-relaxed`}>
                {problem.solutionApproach}
              </p>
            </div>
          </>
        )}

        {styles.showFullContent && (
          <>
            {/* Before/After Comparison */}
            <div className="mb-4 p-3 bg-zinc-800/40 rounded-xl border border-zinc-700/40">
              <div className="grid grid-cols-2 gap-3 text-xs">
                <div className="text-center">
                  <span className="text-red-400 font-medium block mb-1">Before</span>
                  <p className="text-slate-400">{problem.beforeAfter.before}</p>
                </div>
                <div className="text-center">
                  <span className="text-green-400 font-medium block mb-1">After</span>
                  <p className="text-slate-300">{problem.beforeAfter.after}</p>
                </div>
              </div>
            </div>

            {/* Technology Stack */}
            <div className="space-y-2">
              <span className="text-sm font-semibold text-cyan-400 block">Technologies</span>
              <div className="flex flex-wrap gap-1.5">
                {problem.technologies.map((tech) => (
                  <TechBadge key={tech} tech={tech} color={problem.color} />
                ))}
              </div>
            </div>
          </>
        )}

        {/* Hover glow effect */}
        <div className={`absolute inset-0 bg-gradient-to-r ${problem.gradientColor} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-300 pointer-events-none`} />
      </div>
    </motion.div>
  )
}

export function ProblemSolvingSection() {
  return (
    <section className="relative bg-black py-16 px-6">
      <div className="relative z-10 mx-auto max-w-6xl">
        <AnimatedGroup
          variants={{
            container: {
              visible: {
                transition: {
                  staggerChildren: 0.1,
                  delayChildren: 0.2,
                },
              },
            },
            ...transitionVariants,
          }}
        >
          {/* Section Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Workflows That{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-500 italic font-cursive">
                Actually Flow
              </span>
            </h2>
            <p className="text-lg text-slate-400 max-w-2xl mx-auto">
              I identify inefficiencies and build solutions that save time and reduce frustration
            </p>
          </div>

          {/* Innovative Asymmetrical Layout */}
          <div className="relative mb-12">
            {/* Desktop Layout - Asymmetrical Grid */}
            <div className="hidden lg:block">
              <div className="relative h-[600px]">
                {/* Large Featured Card - Left */}
                <div className="absolute top-0 left-0 w-[45%] h-[280px]">
                  <ProblemCard problem={workflowProblems[0]} index={0} variant="large" />
                </div>

                {/* Medium Card - Top Right */}
                <div className="absolute top-0 right-0 w-[50%] h-[200px]">
                  <ProblemCard problem={workflowProblems[1]} index={1} variant="medium" />
                </div>

                {/* Medium Card - Bottom Right */}
                <div className="absolute bottom-0 right-0 w-[50%] h-[200px]">
                  <ProblemCard problem={workflowProblems[2]} index={2} variant="medium" />
                </div>

                {/* Connecting Lines */}
                <div className="absolute top-[140px] left-[45%] w-[5%] h-[1px] bg-gradient-to-r from-cyan-400/50 to-transparent" />
                <div className="absolute top-[300px] right-[50%] w-[5%] h-[1px] bg-gradient-to-l from-cyan-400/50 to-transparent" />
              </div>
            </div>

            {/* Tablet Layout - Staggered */}
            <div className="hidden md:block lg:hidden">
              <div className="space-y-8">
                <div className="flex gap-6">
                  <div className="w-[60%]">
                    <ProblemCard problem={workflowProblems[0]} index={0} variant="medium" />
                  </div>
                  <div className="w-[35%] mt-12">
                    <ProblemCard problem={workflowProblems[1]} index={1} variant="compact" />
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="w-[60%]">
                    <ProblemCard problem={workflowProblems[2]} index={2} variant="medium" />
                  </div>
                </div>
              </div>
            </div>

            {/* Mobile Layout - Vertical Stack */}
            <div className="md:hidden space-y-6">
              {workflowProblems.map((problem, index) => (
                <ProblemCard key={problem.title} problem={problem} index={index} variant="mobile" />
              ))}
            </div>
          </div>

          {/* CTA Button */}
          <div className="text-center">
            <Button
              size="lg"
              className="group bg-gradient-to-r from-cyan-400 to-blue-500 hover:from-cyan-300 hover:to-blue-400 text-black font-semibold px-8 py-3 rounded-lg transition-all duration-300 hover:scale-[1.05] hover:shadow-xl hover:shadow-cyan-400/40"
              asChild
            >
              <ScrollLink to="/projects">
                <span className="flex items-center gap-2">
                  See Solutions in Action
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                </span>
              </ScrollLink>
            </Button>
          </div>
        </AnimatedGroup>
      </div>
    </section>
  )
}
