/**
 * Project Data Utilities
 * Helper functions for accessing and manipulating project data
 */

import { projects, PROJECT_METADATA } from './projects-data'
import type { 
  Project, 
  ProjectCategory, 
  // ProjectStatus, 
  ProjectFilters, 
  ProjectSortOptions,
  ProjectStats,
  FeaturedProject
} from './types'
import { FEATURED_PROJECTS_CONFIG } from './constants'

/**
 * Get all projects
 * @returns All projects in the portfolio
 */
export const getAllProjects = (): readonly Project[] => {
  return projects
}

/**
 * Get featured projects for homepage display
 * Limits to the maximum number of featured projects defined in constants
 * @returns Featured projects, limited to max count
 */
export const getFeaturedProjects = (): readonly FeaturedProject[] => {
  // Get projects marked as featured, sorted by priority
  const featured = projects
    .filter(project => project.isFeatured)
    .sort((a, b) => b.priority - a.priority)
    .slice(0, FEATURED_PROJECTS_CONFIG.maxFeaturedProjects)
  
  return featured
}

/**
 * Get a specific project by ID
 * @param id Project ID to find
 * @returns The project with matching ID or undefined if not found
 */
export const getProjectById = (id: string): Project | undefined => {
  return projects.find(project => project.id === id)
}

/**
 * Filter projects based on provided criteria
 * @param filters Filter criteria for projects
 * @returns Filtered projects
 */
export const filterProjects = (filters: ProjectFilters): readonly Project[] => {
  return projects.filter(project => {
    // Skip projects that aren't public
    if (!project.isPublic) return false
    
    // Apply category filter if specified
    if (filters.category && project.category !== filters.category) {
      return false
    }
    
    // Apply status filter if specified
    if (filters.status && project.status !== filters.status) {
      return false
    }
    
    // Apply technology filter if specified
    if (filters.technology && !project.technologies.includes(filters.technology)) {
      return false
    }
    
    // Apply featured filter if specified
    if (filters.featured !== undefined && project.isFeatured !== filters.featured) {
      return false
    }
    
    return true
  })
}

/**
 * Sort projects based on provided criteria
 * @param projects Projects to sort
 * @param options Sorting options
 * @returns Sorted projects
 */
export const sortProjects = (
  projectsToSort: readonly Project[],
  options: ProjectSortOptions
): readonly Project[] => {
  const { sortBy, order } = options
  
  return [...projectsToSort].sort((a, b) => {
    let comparison = 0
    
    switch (sortBy) {
      case 'priority':
        comparison = a.priority - b.priority
        break
      case 'startDate':
        comparison = new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
        break
      case 'title':
        comparison = a.title.localeCompare(b.title)
        break
      case 'status':
        comparison = a.status.localeCompare(b.status)
        break
      default:
        comparison = 0
    }
    
    return order === 'asc' ? comparison : -comparison
  })
}

/**
 * Get comprehensive project statistics
 * @returns Statistics about the project portfolio
 */
export const getProjectStats = (): ProjectStats => {
  const categoriesCount: Record<ProjectCategory, number> = {
    'Frontend': 0,
    'Full-Stack': 0,
    'Mobile': 0
  }
  
  // Count projects by category
  projects.forEach(project => {
    categoriesCount[project.category as ProjectCategory]++
  })
  
  // Calculate average duration (assuming duration is in format "X weeks")
  const totalWeeks = projects.reduce((total, project) => {
    const weeks = parseInt(project.duration.split(' ')[0]) || 0
    return total + weeks
  }, 0)
  
  const averageDuration = projects.length > 0 
    ? `${Math.round(totalWeeks / projects.length)} weeks`
    : '0 weeks'
  
  return {
    totalProjects: projects.length,
    completedProjects: projects.filter(p => p.status === 'Completed').length,
    inProgressProjects: projects.filter(p => p.status === 'In Progress').length,
    featuredProjects: projects.filter(p => p.isFeatured).length,
    technologiesUsed: Array.from(new Set(projects.flatMap(p => p.technologies))),
    categoriesCount,
    averageDuration
  }
}

/**
 * Get all unique technologies used across projects
 * @returns Array of unique technology names
 */
export const getAllTechnologies = (): string[] => {
  return PROJECT_METADATA.technologies
}

/**
 * Get all unique project categories
 * @returns Array of unique project categories
 */
export const getAllCategories = (): ProjectCategory[] => {
  return PROJECT_METADATA.categories as ProjectCategory[]
}
