'use client'
import React, { useRef } from 'react'
import { motion, useScroll, useTransform, useSpring, useMotionValue } from 'framer-motion'
import { ArrowRight } from 'lucide-react'
import { ScrollLink } from '@/components/ui/scroll-link'
import { useProjectCounts } from '@/hooks/use-project-stats'

// Growth milestone data interface
interface GrowthMilestone {
  year: string
  title: string
  description: string
  metrics: {
    value: string
    label: string
  }[]
  category: 'skills' | 'knowledge' | 'community' | 'opensource'
  color: string
}

// Updated growth milestones focusing on frontend development and junior-level skills
// Note: Project count is now dynamic and will be updated in the component


// Enhanced milestone card component
const MilestoneCard: React.FC<{ milestone: GrowthMilestone; index: number }> = ({ milestone }) => {
  const cardRef = useRef<HTMLDivElement>(null)
  const [isHovered, setIsHovered] = React.useState(false)
  
  const { scrollYProgress } = useScroll({
    target: cardRef,
    offset: ["start end", "end start"]
  })

  // Smoother animations with refined spring configs
  const y = useSpring(
    useTransform(scrollYProgress, [0, 1], [50, -50]),
    { stiffness: 50, damping: 20 }
  )

  const opacity = useSpring(
    useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]),
    { stiffness: 50, damping: 20 }
  )

  const scale = useSpring(
    useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.98, 1, 1, 0.98]),
    { stiffness: 100, damping: 30 }
  )

  // Enhanced mouse tracking for premium 3D effect
  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)
  const rotateX = useSpring(useTransform(mouseY, [-300, 300], [3, -3]), { stiffness: 100, damping: 30 })
  const rotateY = useSpring(useTransform(mouseX, [-300, 300], [-3, 3]), { stiffness: 100, damping: 30 })

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left - rect.width / 2
    const y = e.clientY - rect.top - rect.height / 2
    mouseX.set(x)
    mouseY.set(y)
  }

  const handleMouseLeave = () => {
    mouseX.set(0)
    mouseY.set(0)
    setIsHovered(false)
  }
  
  return (
    <motion.div
      ref={cardRef}
      style={{ y, opacity, scale, rotateX, rotateY }}
      className="relative"
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onMouseEnter={() => setIsHovered(true)}
    >
      {/* Optimized year indicator with solid background */}
      <div className="absolute max-[424px]:relative max-[424px]:mb-3 max-[424px]:flex max-[424px]:justify-center min-[425px]:max-[639px]:relative min-[425px]:max-[639px]:mb-3 min-[425px]:max-[639px]:flex min-[425px]:max-[639px]:justify-center sm:absolute sm:-left-20 md:-left-20 lg:-left-24 top-0 flex items-center">
        <motion.div
          className="relative w-16 h-16 max-[424px]:w-12 max-[424px]:h-12 mobile:w-12 mobile:h-12 sm-tablet:w-13 sm-tablet:h-13 sm:w-14 sm:h-14 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-2xl max-[424px]:rounded-lg mobile:rounded-lg sm-tablet:rounded-xl sm:rounded-xl md:rounded-xl lg:rounded-2xl bg-mono-bg border border-mono-border/50 flex items-center justify-center overflow-hidden group z-20"
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
        >
          {/* Subtle gradient background */}
          <motion.div
            className={`absolute inset-0 bg-gradient-to-br ${milestone.color} opacity-0 group-hover:opacity-15 transition-opacity duration-300`}
          />

          {/* Year text */}
          <span className="relative font-mono font-bold text-mono-text text-lg max-[424px]:text-[10px] min-[425px]:max-[639px]:text-xs sm-tablet:text-base sm:text-base md:text-base lg:text-lg tracking-tight z-10">
            {milestone.year}
          </span>

          {/* Simple border glow */}
          <motion.div
            className="absolute inset-1 rounded-xl sm:rounded-lg md:rounded-lg lg:rounded-xl border opacity-0 group-hover:opacity-40 transition-opacity duration-300"
            style={{
              borderColor: milestone.color.split(' ')[1].replace('to-[', '').replace(']', '')
            }}
          />
        </motion.div>

        {/* Simple connecting line - hidden on mobile */}
        <motion.div
          className="h-[1px] w-12 max-[424px]:hidden min-[425px]:max-[639px]:hidden sm:block sm:w-10 md:w-10 lg:w-12"
          style={{
            background: `linear-gradient(to right, ${milestone.color.split(' ')[1].replace('to-[', '').replace(']', '')}, transparent)`,
            scaleX: useSpring(useTransform(scrollYProgress, [0, 0.5], [0, 1]), { stiffness: 100, damping: 30 })
          }}
        />
      </div>
          
      {/* Optimized card design - reduced height and no flicker */}
      <motion.div
        className="relative bg-mono-surface/[0.08] backdrop-blur-xl border border-mono-border/50 rounded-2xl max-[424px]:rounded-lg mobile:rounded-lg sm-tablet:rounded-xl sm:rounded-xl md:rounded-xl lg:rounded-2xl p-6 max-[424px]:p-3 mobile:p-3 sm-tablet:p-4 sm:p-4 md:p-5 lg:p-6 hover:bg-mono-surface/[0.12] hover:border-mono-border transition-all duration-300 group overflow-hidden"
        style={{
          willChange: 'transform',
          backfaceVisibility: 'hidden',
          transform: 'translateZ(0)'
        }}
        whileHover={{
          scale: 1.01,
          transition: { type: "spring", stiffness: 400, damping: 30 }
        }}
      >
        {/* Simple background effect */}
        <motion.div
          className="absolute inset-0 rounded-2xl sm:rounded-xl md:rounded-xl lg:rounded-2xl opacity-0 group-hover:opacity-8 transition-opacity duration-300"
          style={{
            background: `radial-gradient(circle at center, ${milestone.color.split(' ')[1].replace('to-[', '').replace(']', '')} 30%, transparent 70%)`,
          }}
        />

        <div className="relative z-10 space-y-5 max-[424px]:space-y-2 mobile:space-y-2 sm-tablet:space-y-3 sm:space-y-3 md:space-y-4 lg:space-y-5">
          {/* Optimized title */}
          <motion.div className="relative">
            <motion.h3
              className={`text-2xl max-[424px]:text-sm min-[425px]:max-[639px]:text-base sm-tablet:text-xl sm:text-xl md:text-xl lg:text-2xl font-bold tracking-tight bg-gradient-to-r ${milestone.color} bg-clip-text`}
              style={{
                color: isHovered ? 'transparent' : '#E0E0E0',
                transition: 'color 0.3s ease'
              }}
            >
              {milestone.title}
            </motion.h3>

            {/* Simple underline */}
            <motion.div
              className="absolute -bottom-1 left-0 h-[1px] rounded-full"
              style={{
                background: `linear-gradient(to right, ${milestone.color.split(' ')[1].replace('to-[', '').replace(']', '')}, transparent)`
              }}
              initial={{ width: 0 }}
              whileInView={{ width: '50%' }}
              transition={{ duration: 0.6, delay: 0.2 }}
            />
          </motion.div>

          {/* Optimized description */}
          <p className="text-mono-secondary leading-relaxed text-sm max-[424px]:text-[9px] min-[425px]:max-[639px]:text-[10px] sm-tablet:text-xs sm:text-xs md:text-xs lg:text-sm">
            {milestone.description}
          </p>

          {/* Compact metrics grid */}
          <div className="grid grid-cols-3 gap-4 max-[424px]:gap-2 mobile:gap-2 sm-tablet:gap-2.5 sm:gap-2.5 md:gap-3 lg:gap-4">
            {milestone.metrics.map((metric, idx) => (
              <motion.div
                key={idx}
                className="relative text-center bg-mono-surface/[0.05] border border-mono-border/30 rounded-xl max-[424px]:rounded-lg mobile:rounded-lg sm-tablet:rounded-lg sm:rounded-lg md:rounded-lg lg:rounded-xl p-3 max-[424px]:p-1.5 mobile:p-1.5 sm-tablet:p-2 sm:p-2 md:p-2.5 lg:p-3 hover:bg-mono-surface/[0.08] hover:border-mono-border/40 transition-all duration-200"
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{
                  delay: idx * 0.1,
                  duration: 0.4
                }}
                whileHover={{
                  scale: 1.02,
                  transition: { type: "spring", stiffness: 400, damping: 25 }
                }}
              >
                <motion.div
                  className="text-2xl max-[424px]:text-xs min-[425px]:max-[639px]:text-sm sm-tablet:text-lg sm:text-lg md:text-xl lg:text-2xl font-bold text-mono-text font-mono tracking-tight mb-1 max-[424px]:mb-0.5 mobile:mb-0.5 sm-tablet:mb-0.5 sm:mb-0.5 md:mb-0.5 lg:mb-1"
                  style={{
                    color: isHovered ? milestone.color.split(' ')[1].replace('to-[', '').replace(']', '') : '#E0E0E0',
                    transition: 'color 0.3s ease'
                  }}
                >
                  {metric.value}
                </motion.div>
                <div className="text-xs max-[424px]:text-[7px] min-[425px]:max-[639px]:text-[8px] sm-tablet:text-[10px] sm:text-[10px] md:text-[10px] lg:text-xs text-mono-secondary font-medium tracking-wide uppercase">
                  {metric.label}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export function LearningGrowthSection() {
  // Get dynamic project counts
  const projectCounts = useProjectCounts();

  // Dynamic growth milestones with real-time project count
  const growthMilestones: GrowthMilestone[] = [
    {
      year: 'Now',
      title: 'Modern Web Development',
      description: 'Building responsive and interactive web applications with React, TypeScript, and modern CSS. Focusing on creating clean, accessible user interfaces with attention to detail.',
      metrics: [
        { value: projectCounts.frontendWithPlus, label: 'React Projects' },
        { value: '95%', label: 'Lighthouse Score' },
        { value: '100%', label: 'Responsive Design' }
      ],
      category: 'skills',
      color: 'from-[#00FF85] to-[#00CC6A]' // Neon green gradient
    },
    {
      year: 'Focus',
      title: 'Frontend Excellence',
      description: 'Mastering component-based architecture with React and Next.js. Learning advanced state management, performance optimization, and modern development workflows.',
      metrics: [
        { value: '6+', label: 'Frontend Tools' },
        { value: '15+', label: 'UI Components' },
        { value: '4', label: 'CSS Frameworks' }
      ],
      category: 'knowledge',
      color: 'from-[#FF0099] to-[#CC007A]' // Vivid pink gradient
    },
    {
      year: 'Skills',
      title: 'UI/UX Development',
      description: 'Creating responsive and accessible user interfaces with modern CSS frameworks. Learning design principles and user experience best practices for better web applications.',
      metrics: [
        { value: '12+', label: 'UI Components' },
        { value: '3', label: 'CSS Frameworks' },
        { value: '2', label: 'Design Tools' }
      ],
      category: 'skills',
      color: 'from-[#1E90FF] to-[#1873CC]' // Electric blue gradient
    },
    {
      year: 'Tools',
      title: 'Development Workflow',
      description: 'Using modern development tools for efficient coding and collaboration. Learning Git workflows, code quality tools, and deployment platforms for professional development.',
      metrics: [
        { value: '6+', label: 'Dev Tools' },
        { value: '2', label: 'Deployment Platforms' },
        { value: '100%', label: 'Version Control' }
      ],
      category: 'opensource',
      color: 'from-[#8B31CD] to-[#6F27A3]' // Purple gradient
    },
    {
      year: 'Next',
      title: 'Expanding Horizons',
      description: 'Continuously learning new frontend technologies and best practices. Currently exploring advanced React patterns, testing methodologies, and performance optimization techniques.',
      metrics: [
        { value: '3', label: 'New Technologies' },
        { value: '2', label: 'Learning Courses' },
        { value: '∞', label: 'Growth Mindset' }
      ],
      category: 'community',
      color: 'from-[#FF6B6B] to-[#CC5555]' // Coral gradient
    }
  ];

  return (
    <section className="relative bg-mono-bg py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32 overflow-hidden">
      {/* Enhanced premium background effects */}
      <div className="absolute inset-0 opacity-40">
        {/* Primary ambient glow */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-[800px] h-[800px] bg-gradient-to-r from-[#00FF85]/8 to-[#1E90FF]/8 rounded-full blur-[150px]"
          animate={{
            y: [-30, 30],
            x: [-15, 15],
            opacity: [0.08, 0.15, 0.08],
            scale: [1, 1.15, 1],
            rotate: [0, 180, 360],
            transition: {
              duration: 20,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }
          }}
        />

        {/* Secondary ambient glow */}
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-[1000px] h-[1000px] bg-gradient-to-r from-[#FF0099]/6 to-[#8B31CD]/6 rounded-full blur-[180px]"
          animate={{
            y: [30, -30],
            x: [15, -15],
            opacity: [0.06, 0.12, 0.06],
            scale: [1.1, 1, 1.1],
            rotate: [360, 180, 0],
            transition: {
              duration: 25,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }
          }}
        />

        {/* Tertiary accent glow */}
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-[#FF6B6B]/5 to-transparent rounded-full blur-[120px]"
          animate={{
            scale: [0.8, 1.2, 0.8],
            opacity: [0.05, 0.1, 0.05],
            rotate: [0, 360],
            transition: {
              duration: 30,
              repeat: Infinity,
              ease: "easeInOut"
            }
          }}
        />

        {/* Floating particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 rounded-full bg-mono-accent/20"
            style={{
              left: `${10 + i * 15}%`,
              top: `${20 + (i % 3) * 30}%`
            }}
            animate={{
              y: [-20, -40, -20],
              x: [-10, 10, -10],
              opacity: [0.2, 0.6, 0.2],
              scale: [1, 1.5, 1]
            }}
            transition={{
              duration: 8 + i * 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 1.5
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-5xl mx-auto px-8 max-[424px]:px-8 mobile:px-8 min-[640px]:px-4 md:px-8 lg:px-8 max-[424px]:max-w-4xl mobile:max-w-4xl">
        {/* Match beyond-the-code section content width */}
        {/* Optimized section header */}
        <div className="text-center mb-16 max-[424px]:mb-8 mobile:mb-8 sm-tablet:mb-10 md:mb-12 lg:mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-2 max-[424px]:space-y-1.5 mobile:space-y-1.5 md:space-y-1.5 lg:space-y-2"
          >
            <p className="text-mono-accent text-sm font-medium tracking-widest uppercase">
              Current Expertise
            </p>
            <h2 className="text-4xl max-[424px]:text-3xl min-[425px]:max-[639px]:text-3xl sm-tablet:text-3xl md:text-4xl lg:text-4xl font-bold text-mono-text leading-tight max-[424px]:leading-tight mobile:leading-tight tracking-tight">
              Skills{' '}
              <span className="relative inline-block">
                <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                  Overview
                </span>
                <motion.span
                  className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-20"
                  animate={{
                    opacity: [0.2, 0.3, 0.2],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />
              </span>
            </h2>
          </motion.div>
        </div>

        {/* Optimized timeline layout - centered for mobile - Match beyond-the-code section content width */}
        <div className="relative max-[424px]:text-center mobile:text-center sm:pl-24 md:pl-24 lg:pl-28">
          {/* Simple timeline line - hidden on mobile for better centering */}
          <motion.div
            className="absolute max-[424px]:hidden min-[425px]:max-[639px]:hidden sm:block sm:left-[2.5rem] md:left-[2.5rem] lg:left-[3rem] top-0 bottom-0 w-[1px]"
            style={{
              background: "linear-gradient(180deg, rgba(136, 136, 136, 0.3) 0%, rgba(68, 68, 68, 0.4) 50%, transparent 100%)"
            }}
            initial={{ scaleY: 0, opacity: 0 }}
            whileInView={{ scaleY: 1, opacity: 1 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            viewport={{ once: true }}
          />

          {/* Subtle gradient overlay - hidden on mobile for better centering */}
          <motion.div
            className="absolute max-[424px]:hidden min-[425px]:max-[639px]:hidden sm:block sm:left-[2.5rem] md:left-[2.5rem] lg:left-[3rem] top-0 bottom-0 w-[1px]"
            style={{
              background: "linear-gradient(180deg, #00FF85 0%, #1E90FF 25%, #FF0099 50%, #8B31CD 75%, #FF6B6B 100%)",
              opacity: 0.15
            }}
            initial={{ scaleY: 0 }}
            whileInView={{ scaleY: 1 }}
            transition={{ duration: 2, ease: "easeOut", delay: 0.3 }}
            viewport={{ once: true }}
          />

          {/* Optimized milestones spacing */}
          <div className="space-y-20 max-[424px]:space-y-10 mobile:space-y-10 sm-tablet:space-y-12 sm:space-y-12 md:space-y-16 lg:space-y-20">
            {growthMilestones.map((milestone, index) => (
              <MilestoneCard
                key={milestone.year}
                milestone={milestone}
                index={index}
              />
            ))}
          </div>
        </div>

        {/* Optimized CTA Section */}
        <div className="text-center mt-16 max-[424px]:mt-8 mobile:mt-8 sm-tablet:mt-10 sm:mt-10 md:mt-12 lg:mt-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <ScrollLink
              to="/skills"
              className="group relative overflow-hidden inline-flex items-center gap-3 max-[424px]:gap-2.5 sm:gap-2.5 bg-mono-surface/[0.08] backdrop-blur-xl border border-mono-border/40 hover:border-mono-accent/50 text-[#E0E0E0] font-medium px-6 py-3 max-[424px]:px-5 max-[424px]:py-2.5 sm:px-5 sm:py-2.5 md:px-5 md:py-2.5 lg:px-6 lg:py-3 rounded-xl transition-all duration-300 hover:scale-102"
            >
              {/* Simple background effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4]/5 to-[#8B31CD]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              />

              {/* Subtle glow */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4]/8 to-[#8B31CD]/8 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300"
              />

              <span className="relative z-10 text-base max-[424px]:text-xs min-[425px]:max-[639px]:text-sm sm:text-sm md:text-sm lg:text-base">Explore my skills</span>

              <motion.div
                className="relative z-10"
                animate={{ x: [0, 2, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              >
                <ArrowRight className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4 group-hover:scale-105 transition-transform duration-200" />
              </motion.div>
            </ScrollLink>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
