'use client'
import { useEffect, useRef } from 'react'

interface PerformanceMetrics {
  componentName: string
  renderTime: number
  timestamp: number
}

export function usePerformanceMonitor(componentName: string) {
  const renderStartTime = useRef<number>(0)
  const isFirstRender = useRef(true)

  useEffect(() => {
    if (isFirstRender.current) {
      renderStartTime.current = performance.now()
      isFirstRender.current = false
    }
  })

  useEffect(() => {
    if (renderStartTime.current > 0) {
      const renderTime = performance.now() - renderStartTime.current
      
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        const metrics: PerformanceMetrics = {
          componentName,
          renderTime,
          timestamp: Date.now()
        }
        
        console.log(`🚀 Performance: ${componentName} rendered in ${renderTime.toFixed(2)}ms`)
        
        // Warn if render time is too high
        if (renderTime > 16) { // 60fps = 16.67ms per frame
          console.warn(`⚠️ Performance Warning: ${componentName} took ${renderTime.toFixed(2)}ms to render (>16ms)`)
        }
      }
    }
  })

  return {
    markRenderStart: () => {
      renderStartTime.current = performance.now()
    },
    markRenderEnd: () => {
      if (renderStartTime.current > 0) {
        const renderTime = performance.now() - renderStartTime.current
        return renderTime
      }
      return 0
    }
  }
}

// Hook for measuring intersection observer performance
export function useIntersectionPerformance(elementRef: React.RefObject<HTMLElement>, componentName: string) {
  useEffect(() => {
    if (!elementRef.current) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (process.env.NODE_ENV === 'development') {
            console.log(`👁️ Intersection: ${componentName} visibility: ${(entry.intersectionRatio * 100).toFixed(1)}%`)
          }
        })
      },
      {
        threshold: [0, 0.25, 0.5, 0.75, 1],
        rootMargin: '50px'
      }
    )

    observer.observe(elementRef.current)

    return () => observer.disconnect()
  }, [elementRef, componentName])
}
