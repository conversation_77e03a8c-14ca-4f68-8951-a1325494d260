# Unified Spacing System Guide

This guide explains how to use the centralized spacing system to maintain consistent spacing and container widths across all sections.

## Overview

The unified spacing system is located in `src/styles/spacing.ts` and provides:
- Consistent container widths
- Standardized padding configurations
- Unified margin settings
- Pre-configured section layouts

## Breakpoint System

We use a specific breakpoint system for mobile devices:
- `max-[424px]` - Small mobile devices (≤424px)
- `mobile` - Standard mobile devices (425px-639px) 
- `sm` - Small tablets (640px-767px)
- `md` - Tablets (768px-1023px)
- `lg` - Desktop (≥1024px)

## Usage Examples

### 1. Basic Container Setup

```tsx
import { sectionConfigs } from '@/styles/spacing';

// For about hero section
<div className={sectionConfigs.aboutHero.container}>
  {/* Content */}
</div>

// For other sections
<div className={getContainerClasses('content')}>
  {/* Content */}
</div>
```

### 2. Profile Card Sizing

```tsx
// Use predefined profile card configuration
<div className={sectionConfigs.aboutHero.profileCard}>
  {/* Profile content */}
</div>
```

### 3. Text Content Containers

```tsx
// Use predefined text content configuration
<div className={sectionConfigs.aboutHero.textContent}>
  {/* Text content */}
</div>
```

### 4. Button Containers

```tsx
// Use predefined button container configuration
<div className={sectionConfigs.aboutHero.buttonContainer}>
  {/* Buttons */}
</div>
```

## Container Width Configurations

### Available Container Types
- `hero`: `max-w-6xl` - For hero sections
- `content`: `max-w-4xl` - For main content sections
- `narrow`: `max-w-3xl` - For narrow content
- `wide`: `max-w-7xl` - For wide layouts

### Mobile Width Constraints
All containers automatically include:
- `max-[424px]:max-w-[95%]` - 95% width for small mobile
- `mobile:max-w-[95%]` - 95% width for standard mobile

## Padding System

### Container Padding
- Desktop: `md:px-8`
- Tablet: `px-4`
- Small Mobile: `max-[424px]:px-2`
- Standard Mobile: `mobile:px-2`

### Content Padding
- Text content: Responsive padding for text elements
- Card content: Responsive padding for card/component elements

## Margin System

### Section Margins
- Desktop: `lg:mb-8`
- Tablet: `md:mb-4`
- Small Mobile: `max-[424px]:mb-4`
- Standard Mobile: `mobile:mb-4`

### Content Margins
- Desktop: `lg:mb-6`
- Tablet: `md:mb-4`
- Small Mobile: `max-[424px]:mb-3`
- Standard Mobile: `mobile:mb-3`

## Implementation Steps

### Step 1: Import the Spacing System
```tsx
import { sectionConfigs, getContainerClasses } from '@/styles/spacing';
```

### Step 2: Replace Existing Classes
Replace manual spacing classes with unified configurations:

**Before:**
```tsx
<div className="max-w-6xl mx-auto px-6 md:px-8 mb-6 min-[425px]:mb-4">
```

**After:**
```tsx
<div className={`${sectionConfigs.aboutHero.container} mb-6 max-[424px]:mb-4 mobile:mb-4`}>
```

### Step 3: Update Mobile Breakpoints
Replace `min-[425px]` with our standardized breakpoints:
- `min-[425px]` → `mobile` (for 425px-639px)
- Add `max-[424px]` for ≤424px when needed

## Section-Specific Configurations

### About Hero Section
- Container: Full hero container with unified spacing
- Profile Card: Responsive card sizing for all breakpoints
- Text Content: Centered text with proper width constraints
- Button Container: Responsive button layout

### Future Sections
Add new section configurations to `sectionConfigs` object:

```tsx
export const sectionConfigs = {
  // Existing configurations...
  
  newSection: {
    container: getContainerClasses('content'),
    // Add specific configurations for new section
  }
} as const;
```

## Benefits

1. **Consistency**: All sections use the same spacing rules
2. **Maintainability**: Change spacing in one place affects all sections
3. **Responsive**: Automatic responsive behavior across all breakpoints
4. **Scalability**: Easy to add new sections with consistent spacing
5. **Performance**: Reduced CSS bundle size through reusable classes

## Next Steps

1. Apply this system to other sections (projects, skills, contact)
2. Update existing components to use unified breakpoints
3. Create additional section configurations as needed
4. Document any custom spacing requirements

## Troubleshooting

### Common Issues
1. **Overlapping breakpoints**: Ensure `max-[424px]` and `mobile` don't conflict
2. **Missing imports**: Always import from `@/styles/spacing`
3. **Class conflicts**: Remove old spacing classes when applying new system

### Testing
Test on all breakpoint ranges:
- ≤424px (small mobile)
- 425px-639px (standard mobile)
- 640px-767px (small tablet)
- 768px-1023px (tablet)
- ≥1024px (desktop)
