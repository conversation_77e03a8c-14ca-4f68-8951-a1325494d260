/**
 * Project Data Types
 * Comprehensive TypeScript interfaces for project data management
 */

// Project categories
export type ProjectCategory = 'Frontend' | 'Full-Stack' | 'Mobile'

// Project status options
export type ProjectStatus = 'Completed' | 'In Progress' | 'Planned'

// Technology categories for organization
export type TechCategory = 'Frontend' | 'Backend' | 'Database' | 'API' | 'Tools'

// Main project interface - comprehensive structure for all project data
export interface Project {
  // Core Information
  id: string                      // Unique identifier for the project
  title: string                   // Project name/title
  description: string             // Detailed project description
  problemStatement: string        // What problem this project solves
  
  // Visual Assets
  image: string                   // Project screenshot/preview image path
  imageAlt: string               // Alt text for accessibility
  
  // Technical Details
  technologies: readonly string[] // Tech stack used in the project
  features: readonly string[]     // Key features and capabilities
  category: ProjectCategory       // Project type classification
  
  // Links
  liveUrl: string                // Live demo URL
  githubUrl: string              // GitHub repository URL
  
  // Project Details
  status: ProjectStatus          // Current project status
  duration: string               // Time taken to complete (e.g., "4 weeks")
  team: string                   // Team composition (e.g., "Solo project")
  startDate: string              // Project start date (ISO format)
  
  // Visual Styling
  accentColor: string            // Primary accent color for the project
  cardStyle: {                   // Card styling configuration
    readonly background: string   // Background gradient classes
    readonly glow: string        // Glow effect classes
  }
  
  // Additional Details
  highlights: readonly string[]   // Key achievements or standout features
  keywords: readonly string[]     // SEO and search keywords
  
  // Flags and Sorting
  isFeatured: boolean            // Whether to show in featured section
  isPublic: boolean              // Whether project is publicly visible
  priority: number               // Sorting priority (higher = more important)
}

// Simplified project interface for featured projects display
export type FeaturedProject = Pick<Project,
  'id' | 'title' | 'problemStatement' | 'description' | 'image' | 'imageAlt' |
  'technologies' | 'features' | 'liveUrl' | 'githubUrl' | 'category' |
  'accentColor' | 'cardStyle'>

// Project statistics interface
export interface ProjectStats {
  totalProjects: number
  completedProjects: number
  inProgressProjects: number
  featuredProjects: number
  technologiesUsed: string[]
  categoriesCount: Record<ProjectCategory, number>
  averageDuration: string
}

// Project filter options
export interface ProjectFilters {
  category?: ProjectCategory
  status?: ProjectStatus
  technology?: string
  featured?: boolean
}

// Project sorting options
export type ProjectSortBy = 'priority' | 'startDate' | 'title' | 'status'
export type ProjectSortOrder = 'asc' | 'desc'

export interface ProjectSortOptions {
  sortBy: ProjectSortBy
  order: ProjectSortOrder
}

// Type guards for runtime type checking
export const isProject = (obj: any): obj is Project => {
  return (
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.title === 'string' &&
    typeof obj.description === 'string' &&
    typeof obj.problemStatement === 'string' &&
    typeof obj.image === 'string' &&
    typeof obj.imageAlt === 'string' &&
    Array.isArray(obj.technologies) &&
    Array.isArray(obj.features) &&
    typeof obj.category === 'string' &&
    typeof obj.liveUrl === 'string' &&
    typeof obj.githubUrl === 'string' &&
    typeof obj.status === 'string' &&
    typeof obj.duration === 'string' &&
    typeof obj.team === 'string' &&
    typeof obj.startDate === 'string' &&
    typeof obj.accentColor === 'string' &&
    typeof obj.cardStyle === 'object' &&
    typeof obj.cardStyle.background === 'string' &&
    typeof obj.cardStyle.glow === 'string' &&
    Array.isArray(obj.highlights) &&
    Array.isArray(obj.keywords) &&
    typeof obj.isFeatured === 'boolean' &&
    typeof obj.isPublic === 'boolean' &&
    typeof obj.priority === 'number'
  )
}
