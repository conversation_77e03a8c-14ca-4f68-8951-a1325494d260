'use client'
import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Calendar, Clock, ArrowRight, Tag, Code, Database, Server, Palette, Shield, TrendingUp } from 'lucide-react'

// Category data interface
interface Category {
  name: string
  description: string
  count: number
  color: string
  icon: React.ReactNode
  articles: Article[]
}

interface Article {
  title: string
  excerpt: string
  date: string
  readTime: string
  tags: string[]
  image: string
}

// Categories data with consistent styling
const categories: Category[] = [
  {
    name: 'React',
    description: 'Component architecture, hooks, and modern React patterns',
    count: 8,
    color: '#00FF85',
    icon: <Code className="w-5 h-5" />,
    articles: [
      {
        title: 'Building Scalable React Applications with TypeScript',
        excerpt: 'Learn how to structure large React applications using TypeScript and best practices.',
        date: '2024-01-15',
        readTime: '8 min read',
        tags: ['React', 'TypeScript', 'Architecture'],
        image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      },
      {
        title: 'Advanced React Hooks Patterns',
        excerpt: 'Explore custom hooks, context patterns, and performance optimization techniques.',
        date: '2024-01-08',
        readTime: '6 min read',
        tags: ['React', 'Hooks', 'Performance'],
        image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      }
    ]
  },
  {
    name: 'Node.js',
    description: 'Backend development, APIs, and server-side optimization',
    count: 6,
    color: '#1E90FF',
    icon: <Server className="w-5 h-5" />,
    articles: [
      {
        title: 'Node.js Performance Optimization Strategies',
        excerpt: 'Discover proven techniques to optimize Node.js applications for better performance.',
        date: '2024-01-05',
        readTime: '10 min read',
        tags: ['Node.js', 'Performance', 'Backend'],
        image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      }
    ]
  },
  {
    name: 'CSS',
    description: 'Modern styling techniques, animations, and responsive design',
    count: 5,
    color: '#FF0099',
    icon: <Palette className="w-5 h-5" />,
    articles: [
      {
        title: 'Modern CSS Techniques for Better User Interfaces',
        excerpt: 'Explore the latest CSS features including Grid, Flexbox, and Container Queries.',
        date: '2024-01-10',
        readTime: '6 min read',
        tags: ['CSS', 'UI/UX', 'Frontend'],
        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      }
    ]
  },
  {
    name: 'Database',
    description: 'SQL, NoSQL, schema design, and data optimization',
    count: 4,
    color: '#FFA500',
    icon: <Database className="w-5 h-5" />,
    articles: [
      {
        title: 'Database Design Patterns for Modern Applications',
        excerpt: 'Understanding when to use SQL vs NoSQL databases and schema design principles.',
        date: '2023-12-28',
        readTime: '7 min read',
        tags: ['Database', 'SQL', 'NoSQL'],
        image: 'https://images.unsplash.com/photo-1544383835-bda2bc66a55d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      }
    ]
  },
  {
    name: 'Security',
    description: 'Authentication, authorization, and security best practices',
    count: 3,
    color: '#DC143C',
    icon: <Shield className="w-5 h-5" />,
    articles: [
      {
        title: 'Implementing Authentication in Modern Web Apps',
        excerpt: 'A comprehensive guide to implementing secure authentication using JWT and OAuth.',
        date: '2023-12-15',
        readTime: '12 min read',
        tags: ['Authentication', 'Security', 'JWT'],
        image: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      }
    ]
  },
  {
    name: 'Trends',
    description: 'Emerging technologies and future of web development',
    count: 4,
    color: '#9370DB',
    icon: <TrendingUp className="w-5 h-5" />,
    articles: [
      {
        title: 'The Future of Web Development: Trends to Watch',
        excerpt: 'Exploring emerging technologies and trends shaping the future of web development.',
        date: '2023-12-20',
        readTime: '5 min read',
        tags: ['Web Development', 'Trends', 'Future'],
        image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      }
    ]
  }
]

export function BlogCategoriesSection() {
  const [selectedCategory, setSelectedCategory] = useState<string>('All')
  
  const allArticles = categories.flatMap(cat => cat.articles)
  const filteredArticles = selectedCategory === 'All' 
    ? allArticles 
    : categories.find(cat => cat.name === selectedCategory)?.articles || []

  return (
    <section className="relative bg-mono-bg py-24 lg:py-32">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.p 
            className="text-mono-secondary text-sm tracking-wide uppercase font-medium mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            EXPLORE BY TOPIC
          </motion.p>
          
          <motion.h2 
            className="text-3xl md:text-5xl font-bold text-mono-text mb-6 leading-tight tracking-tight"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Article{' '}
            <span className="italic font-cursive text-transparent bg-clip-text bg-gradient-to-r from-accent-vivid to-accent-neon">
              Categories
            </span>
          </motion.h2>
        </motion.div>

        {/* Categories grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-12"
        >
          <button
            onClick={() => setSelectedCategory('All')}
            className={`p-4 rounded-2xl border transition-all duration-300 ${
              selectedCategory === 'All'
                ? 'bg-mono-surface/20 border-accent-neon text-accent-neon'
                : 'bg-mono-surface/[0.08] border-mono-border text-mono-secondary hover:bg-mono-surface/[0.12] hover:text-mono-text'
            }`}
          >
            <div className="text-center">
              <Tag className="w-5 h-5 mx-auto mb-2" />
              <div className="font-semibold text-sm">All</div>
              <div className="text-xs opacity-70">{allArticles.length} articles</div>
            </div>
          </button>
          
          {categories.map((category, index) => (
            <motion.button
              key={category.name}
              onClick={() => setSelectedCategory(category.name)}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
              viewport={{ once: true }}
              className={`p-4 rounded-2xl border transition-all duration-300 ${
                selectedCategory === category.name
                  ? 'bg-mono-surface/20 border-opacity-60 text-mono-text'
                  : 'bg-mono-surface/[0.08] border-mono-border text-mono-secondary hover:bg-mono-surface/[0.12] hover:text-mono-text'
              }`}
              style={{
                borderColor: selectedCategory === category.name ? category.color : undefined,
                color: selectedCategory === category.name ? category.color : undefined
              }}
            >
              <div className="text-center">
                <div className="mb-2 flex justify-center">
                  {category.icon}
                </div>
                <div className="font-semibold text-sm">{category.name}</div>
                <div className="text-xs opacity-70">{category.count} articles</div>
              </div>
            </motion.button>
          ))}
        </motion.div>

        {/* Articles list */}
        <motion.div
          key={selectedCategory}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-6"
        >
          {filteredArticles.map((article, index) => (
            <motion.article
              key={article.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-mono-surface/[0.08] border border-mono-border rounded-2xl p-6 backdrop-blur-xl hover:bg-mono-surface/[0.12] transition-all duration-300 group cursor-pointer"
            >
              <div className="grid md:grid-cols-4 gap-6">
                <div className="md:col-span-1">
                  <img
                    src={article.image}
                    alt={article.title}
                    className="w-full h-32 md:h-24 object-cover rounded-xl group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="md:col-span-3 space-y-3">
                  <div className="flex items-center gap-4 text-mono-secondary text-sm">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {new Date(article.date).toLocaleDateString()}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {article.readTime}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-mono-text group-hover:text-accent-neon transition-colors duration-300">
                    {article.title}
                  </h3>
                  <p className="text-mono-secondary leading-relaxed">{article.excerpt}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex flex-wrap gap-2">
                      {article.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-mono-surface/30 text-mono-secondary text-xs rounded-lg"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                    <ArrowRight className="w-5 h-5 text-accent-neon group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                </div>
              </div>
            </motion.article>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
