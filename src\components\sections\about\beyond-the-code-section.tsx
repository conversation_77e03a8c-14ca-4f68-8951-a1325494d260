'use client'
import React, { useState, useRef } from "react";
import { motion } from "framer-motion";
import { Music, Gamepad2, Zap, Users, Volume2, VolumeX, Maximize2 } from "lucide-react";
import { sectionConfigs } from "@/styles/spacing";

interface Interest {
  title: string;
  description: string;
  influence: string;
  icon: React.ReactNode;
  color: string;
  details: string[];
  images: string[];
  className: string;
}

const personalInterests: Interest[] = [
  {
    title: "Basketball",
    description: "Growing together as a team",
    influence: "Playing basketball taught me that amazing things happen when everyone works together. The teamwork and quick decisions I learned on the court help me every day in my projects.",
    icon: <Users className="w-6 h-6" />,
    color: "#1E90FF",
    className: "col-span-1 md:col-span-4 lg:col-span-4 border-b md:border-r dark:border-neutral-800",
    details: [
      "Working together as one team",
      "Making smart, quick decisions",
      "Supporting teammates",
      "Learning from every game"
    ],
    images: ["/images/basketball/team.jpg"]
  },
  {
    title: "Gaming",
    description: "Building fun experiences",
    influence: "Games taught me what makes things fun and easy to use. When I build websites, I think about how to make them feel as smooth and enjoyable as my favorite games.",
    icon: <Gamepad2 className="w-6 h-6" />,
    color: "#00FF85",
    className: "col-span-1 md:col-span-2 lg:col-span-2 border-b dark:border-neutral-800",
    details: [
      "Making things fun to use",
      "Creating smooth experiences",
      "Adding delightful touches",
      "Keeping it simple"
    ],
    images: ["/images/gaming/gaming.MOV"]
  },
  {
    title: "Music",
    description: "Finding my rhythm",
    influence: "Music keeps me focused and creative. When I have the right playlist on, I can solve coding problems and come up with better ideas.",
    icon: <Music className="w-6 h-6" />,
    color: "#FF0099",
    className: "col-span-1 md:col-span-3 lg:col-span-3 border-b md:border-r dark:border-neutral-800",
    details: [
      "Staying in the zone",
      "Finding inspiration",
      "Keeping good rhythm",
      "Enjoying the process"
    ],
    images: ["/images/music/listening.jpg"]
  },
  {
    title: "Always Learning",
    description: "Getting better every day",
    influence: "I'm always curious about new things. Learning keeps me excited and helps me find better ways to build websites and solve problems.",
    icon: <Zap className="w-6 h-6" />,
    color: "#8B31CD",
    className: "col-span-1 md:col-span-3 lg:col-span-3 border-b md:border-none",
    details: [
      "Learning new skills",
      "Sharing knowledge",
      "Solving problems",
      "Growing together"
    ],
    images: ["/images/learning/learning.jpg"]
  }
];

const FeatureCard = ({
  children,
  className,
}: {
  children?: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={`p-4 max-[424px]:p-4 sm:p-4 md:p-5 lg:p-8 relative overflow-hidden rounded-xl ${className}`}>
      {children}
    </div>
  );
};

const FeatureTitle = ({ children }: { children?: React.ReactNode }) => {
  return (
    <h3 className="text-xl max-[424px]:text-lg mobile:text-lg sm:text-lg md:text-lg lg:text-2xl font-bold text-mono-text tracking-tight">
      {children}
    </h3>
  );
};

const FeatureDescription = ({ children }: { children?: React.ReactNode }) => {
  return (
    <p className="text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-sm lg:text-base text-mono-secondary leading-relaxed">
      {children}
    </p>
  );
};

const InterestCard = ({ interest }: { interest: Interest }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);
  
  const imageVariants = {
    initial: { scale: 1, opacity: 0.95 },
    hover: { 
      scale: 1.02,
      opacity: 1,
      transition: { 
        duration: 0.4,
        ease: "easeOut" as const
      }
    }
  };

  const contentVariants = {
    initial: { y: 0 },
    hover: { 
      y: -3,
      transition: { 
        duration: 0.4,
        ease: "easeOut" as const
      }
    }
  };

  const handleFullscreen = () => {
    if (videoRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        videoRef.current.requestFullscreen();
      }
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted;
      setIsMuted(!isMuted);
    }
  };

  // Special layout for Basketball & Gaming
  const isBasketball = interest.title === "Basketball";
  const isGaming = interest.title === "Gaming";
  // const isVideo = interest.images[0].endsWith('.MOV');

  return (
    <motion.div
      className="relative group h-full"
      initial="initial"
      whileHover="hover"
      animate="initial"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      {/* Enhanced gradient overlay with premium glow */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-5 rounded-xl transition-all duration-700"
        style={{ backgroundImage: `linear-gradient(to right, ${interest.color}, transparent)` }}
      />

      {/* Premium border glow effect */}
      <motion.div
        className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-20 transition-all duration-700"
        style={{
          background: `linear-gradient(135deg, ${interest.color}20, transparent)`,
          border: `1px solid ${interest.color}10`
        }}
      />

      <motion.div
        className="relative z-10 space-y-6 max-[424px]:space-y-4 mobile:space-y-4 sm:space-y-4 md:space-y-4 lg:space-y-6 h-full"
        variants={contentVariants}
      >
        {/* Enhanced header section */}
        <div className="flex items-center gap-4 max-[424px]:gap-3 mobile:gap-3 sm:gap-3 md:gap-3 lg:gap-4">
          <motion.div
            className="w-12 h-12 max-[424px]:w-10 max-[424px]:h-10 mobile:w-10 mobile:h-10 sm:w-10 sm:h-10 md:w-10 md:h-10 lg:w-12 lg:h-12 rounded-xl max-[424px]:rounded-lg mobile:rounded-lg sm:rounded-lg md:rounded-lg lg:rounded-xl flex items-center justify-center relative overflow-hidden"
            style={{ backgroundColor: `${interest.color}10` }}
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.3 }}
          >
            {/* Subtle inner glow */}
            <motion.div
              className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-30 transition-opacity duration-500"
              style={{ backgroundColor: `${interest.color}20` }}
            />
            <motion.div
              className="relative z-10"
              style={{ color: interest.color }}
              whileHover={{ rotate: [0, -5, 5, 0] }}
              transition={{ duration: 0.6 }}
            >
              {interest.icon}
            </motion.div>
          </motion.div>
          <div className="flex-1">
            <FeatureTitle>{interest.title}</FeatureTitle>
            <FeatureDescription>{interest.description}</FeatureDescription>
          </div>
        </div>
        
        {/* Media section with conditional layout */}
        {isBasketball ? (
          <div className="relative aspect-[16/9] rounded-xl overflow-hidden">
            {/* Artistic poster design for basketball */}
            <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-black/40 to-transparent z-20" />
            <motion.div
              className="absolute inset-0 bg-[#1E90FF]/10"
              animate={{
                opacity: isHovered ? 0.15 : 0.1
              }}
              transition={{ duration: 0.4 }}
            />
            <img
              src={interest.images[0]}
              alt="Basketball team"
              className="absolute inset-0 w-full h-full object-cover"
              loading="lazy"
            />
            
            {/* Dynamic overlay elements
            <div className="absolute inset-0 p-6 max-[424px]:p-4 mobile:p-4 sm:p-4 md:p-4 lg:p-6 flex flex-col justify-between z-30"> */}
              {/* <div className="space-y-3 max-[424px]:space-y-2 mobile:space-y-2 sm:space-y-2 md:space-y-2 lg:space-y-3">
                <motion.div
                  className="text-xs max-[424px]:text-xs mobile:text-xs md:text-xs lg:text-xs font-bold tracking-widest text-[#1E90FF] uppercase"
                  animate={{ opacity: [0.8, 1, 0.8] }}
                  transition={{ duration: 2.5, repeat: Infinity }}
                >
                  Team Spirit
                </motion.div>
                <div className="text-2xl max-[424px]:text-lg mobile:text-lg sm:text-lg md:text-xl lg:text-2xl font-bold text-white leading-tight">
                  Together We Rise
                </div>
              </div> */}

              {/* <div className="space-y-4 max-[424px]:space-y-3 mobile:space-y-3 sm:space-y-3 md:space-y-3 lg:space-y-4">
                <div className="h-px bg-gradient-to-r from-[#1E90FF] to-transparent" />
                <div className="flex items-center gap-3 max-[424px]:gap-2 mobile:gap-2 sm:gap-2 md:gap-2 lg:gap-3">
                  <motion.div
                    className="w-8 h-8 max-[424px]:w-6 max-[424px]:h-6 mobile:w-6 mobile:h-6 sm:w-6 sm:h-6 md:w-6 md:h-6 lg:w-8 lg:h-8 rounded-full bg-[#1E90FF]/20 flex items-center justify-center"
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <Users className="w-4 h-4 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3 sm:h-3 md:w-3 md:h-3 lg:w-4 lg:h-4 text-[#1E90FF]" />
                  </motion.div>
                  <div className="text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm text-white/90">One Team, One Goal</div>
                </div>
              </div> */}
            {/* </div> */}

            {/* Subtle light effects */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-t from-[#1E90FF]/20 to-transparent opacity-0"
              animate={{ opacity: isHovered ? 0.2 : 0 }}
              transition={{ duration: 0.4 }}
            />
          </div>
        ) : isGaming ? (
          <div className="relative aspect-square rounded-xl overflow-hidden bg-black">
            <video
              ref={videoRef}
              src={interest.images[0]}
              autoPlay
              loop
              muted={isMuted}
              playsInline
              preload="metadata"
              className="absolute inset-0 w-full h-full object-cover"
              onError={(e) => {
                console.error('Video failed to load:', e);
                // Fallback to a placeholder or alternative content
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-black/20 to-transparent pointer-events-none" />
            <motion.div
              className="absolute inset-0"
              style={{
                backgroundColor: `${interest.color}20`,
                mixBlendMode: 'overlay'
              }}
              animate={{
                opacity: isHovered ? 0.4 : 0.2
              }}
              transition={{ duration: 0.4 }}
            />
            
            {/* Video Controls */}
            <div className="absolute bottom-4 max-[424px]:bottom-3 mobile:bottom-3 sm:bottom-3 md:bottom-3 lg:bottom-4 right-4 max-[424px]:right-3 mobile:right-3 sm:right-3 md:right-3 lg:right-4 flex items-center gap-3 max-[424px]:gap-2 mobile:gap-2 sm:gap-2 md:gap-2 lg:gap-3 z-30">
              <motion.button
                className="p-2 max-[424px]:p-1.5 mobile:p-1.5 sm:p-1.5 md:p-1.5 lg:p-2 rounded-full bg-black/50 backdrop-blur-sm text-white/90 hover:bg-black/70 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={toggleMute}
              >
                {isMuted ? (
                  <VolumeX className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />
                ) : (
                  <Volume2 className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />
                )}
              </motion.button>
              <motion.button
                className="p-2 max-[424px]:p-1.5 mobile:p-1.5 sm:p-1.5 md:p-1.5 lg:p-2 rounded-full bg-black/50 backdrop-blur-sm text-white/90 hover:bg-black/70 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleFullscreen}
              >
                <Maximize2 className="w-4 h-4 max-[424px]:w-3.5 max-[424px]:h-3.5 mobile:w-3.5 mobile:h-3.5 sm:w-3.5 sm:h-3.5 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4" />
              </motion.button>
            </div>
          </div>
        ) : (
          <div className="relative aspect-[16/9] rounded-xl overflow-hidden">
            <motion.div
              variants={imageVariants}
              className="relative h-full"
            >
              <img
                src={interest.images[0]}
                alt={`${interest.title} image`}
                className="w-full h-full object-cover"
                loading="lazy"
              />
              <motion.div 
                className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              />
            </motion.div>
          </div>
        )}

        {/* Influence section */}
        <div className="space-y-4 max-[424px]:space-y-3 mobile:space-y-3 sm:space-y-3 md:space-y-3 lg:space-y-4">
          <div className="flex items-center gap-2">
            <motion.div
              className="w-1 h-1 rounded-full"
              style={{ backgroundColor: interest.color }}
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.6, 0.8, 0.6]
              }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <span className="text-xs max-[424px]:text-xs mobile:text-xs md:text-xs lg:text-xs font-medium text-mono-accent uppercase tracking-wider">
              How It Helps Me Create
            </span>
          </div>
          <p className="text-mono-secondary text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm leading-relaxed">
            {interest.influence}
          </p>
        </div>
      </motion.div>
    </motion.div>
  );
};

export function BeyondTheCodeSection() {
  return (
    <section className="relative bg-mono-bg py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32 overflow-hidden">
      <div className="absolute inset-0 bg-mono-bg/95 backdrop-blur-3xl -z-10" />

      {/* Enhanced background effects */}
      <motion.div
        className="absolute inset-0 opacity-30 pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.3 }}
        transition={{ duration: 1.5 }}
      >
        <motion.div
          className="absolute top-1/4 left-1/4 w-[900px] h-[900px] bg-gradient-to-r from-[#1E90FF]/10 to-transparent rounded-full blur-[160px]"
          animate={{
            scale: [1, 1.05, 1],
            opacity: [0.1, 0.15, 0.1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-[1100px] h-[1100px] bg-gradient-to-r from-[#00FF85]/10 to-transparent rounded-full blur-[180px]"
          animate={{
            scale: [1.05, 1, 1.05],
            opacity: [0.1, 0.15, 0.1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-[#FF0099]/8 to-transparent rounded-full blur-[140px]"
          animate={{
            scale: [1, 1.03, 1],
            opacity: [0.08, 0.12, 0.08],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
      </motion.div>

      {/* Section header */}
      <div className={`relative z-10 ${sectionConfigs.beyondTheCodeSection.headerContainer} lg:max-w-4xl`}>
        <motion.div
          initial={{ opacity: 0, y: 15 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.8,
            type: 'spring',
            stiffness: 100,
            damping: 15
          }}
          viewport={{ once: true }}
          className="space-y-3 max-[424px]:space-y-2 mobile:space-y-2 md:space-y-2 lg:space-y-3 text-center"
        >
          <p className="text-mono-accent text-sm max-[424px]:text-xs mobile:text-xs md:text-xs lg:text-sm font-medium tracking-widest uppercase">
            Interests & Hobbies
          </p>
          <h2 className="text-3xl max-[424px]:text-3xl mobile:text-3xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight tracking-tight">
            Beyond{' '}
            <span className="relative inline-block">
              <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                the code
              </span>
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-20"
                animate={{
                  opacity: [0.15, 0.2, 0.15],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />
            </span>
          </h2>
        </motion.div>
      </div>

      <div className={`relative z-10 ${sectionConfigs.beyondTheCodeSection.contentContainer} lg:max-w-4xl`}>
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 1 }}
          viewport={{ once: true }}
          className={`grid grid-cols-1 md:grid-cols-6 lg:grid-cols-6 ${sectionConfigs.beyondTheCodeSection.gridSpacing}`}
        >
          {personalInterests.map((interest, index) => (
            <motion.div
              key={interest.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={interest.className}
            >
              <FeatureCard>
                <InterestCard interest={interest} />
              </FeatureCard>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
