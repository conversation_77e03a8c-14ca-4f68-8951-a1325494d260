import React, { useRef } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';
import { Code, Smartphone, Zap } from 'lucide-react';
import { useProjectCounts } from '@/hooks/use-project-stats';

interface SkillCategory {
  title: string;
  description: string;
  stats: {
    value: string;
    label: string;
  }[];
  color: string;
  icon: React.ElementType;
}

// Skill categories - Projects count is now dynamic and will be updated in the component

export function SkillsOverviewSection() {
  const containerRef = useRef<HTMLDivElement>(null);

  // Get dynamic project counts
  const projectCounts = useProjectCounts();

  // Dynamic skill categories with real-time project count
  const skillCategories: SkillCategory[] = [
    {
      title: 'Frontend Development',
      description: 'I build websites with React and modern tools. I focus on making sites that work well and are easy to use.',
      stats: [
        { value: projectCounts.completedWithPlus, label: 'Projects Built' },
        { value: '6', label: 'Technologies' },
        { value: '100%', label: 'Passion' }
      ],
      color: '#00FF85', // Neon green
      icon: Code
    },
    {
      title: 'Responsive Design',
      description: 'I create websites that look great on all devices. From phones to desktops, everything works smoothly.',
      stats: [
        { value: '5+', label: 'UI Components' },
        { value: '3', label: 'Breakpoints' },
        { value: '2', label: 'Design Tools' }
      ],
      color: '#1E90FF', // Electric blue
      icon: Smartphone
    },
    {
      title: 'Modern Workflow',
      description: 'I use the latest tools to write clean code. Git, VS Code, and modern frameworks help me build better websites.',
      stats: [
        { value: '4+', label: 'Dev Tools' },
        { value: '2+', label: 'Frameworks' },
        { value: '1', label: 'Year Learning' }
      ],
      color: '#FF0099', // Vivid pink
      icon: Zap
    }
  ];

  // Refined scroll-based animations for background
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start 0.95", "end 0.05"]
  });

  const y1 = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, -15]),
    { stiffness: 80, damping: 20, restDelta: 0.0005 }
  );

  const y2 = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, 15]),
    { stiffness: 80, damping: 20, restDelta: 0.0005 }
  );

  return (
    <section ref={containerRef} className="relative bg-mono-bg pt-36 pb-20 overflow-hidden">
      {/* Enhanced background effects */}
      <div className="absolute inset-0 opacity-30 pointer-events-none">
        <motion.div
          className="absolute top-1/4 left-1/4 w-[900px] h-[900px] bg-gradient-to-r from-[#00FF85]/10 to-transparent rounded-full blur-[160px]"
          style={{
            y: y1,
            willChange: 'transform'
          }}
          initial={{ opacity: 0.1, scale: 1 }}
          animate={{
            opacity: [0.1, 0.15, 0.1],
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-[1100px] h-[1100px] bg-gradient-to-r from-[#1E90FF]/10 to-transparent rounded-full blur-[180px]"
          style={{
            y: y2,
            willChange: 'transform'
          }}
          initial={{ opacity: 0.1, scale: 1.05 }}
          animate={{
            opacity: [0.1, 0.15, 0.1],
            scale: [1.05, 1, 1.05],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
            delay: 1
          }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-[#FF0099]/8 to-transparent rounded-full blur-[140px]"
          animate={{
            scale: [1, 1.03, 1],
            opacity: [0.08, 0.12, 0.08],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-6 md:px-8 lg:max-w-4xl">
        {/* Section header - updated to match other sections */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.7,
            type: 'spring',
            stiffness: 80,
            damping: 18
          }}
          viewport={{ once: true, margin: "-10%" }}
          className="space-y-3 text-center mb-8"
        >
          <p className="text-mono-accent text-sm font-medium tracking-widest uppercase">
            Frontend Expertise
          </p>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight tracking-tight">
            Skills{' '}
            <span className="relative inline-block">
              <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                Overview
              </span>
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-15"
                initial={{ opacity: 0.15 }}
                animate={{
                  opacity: [0.15, 0.22, 0.15],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
              />
            </span>
          </h2>
        </motion.div>

        {/* Skills layout - alternating sides on desktop, stacked on mobile */}
        <div className="relative">


          {/* Skills categories - stacked on mobile, alternating layout on desktop */}
          <div className="space-y-8 md:space-y-20 lg:space-y-28">
            {skillCategories.map((category, index) => (
              <motion.div
                key={category.title}
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true, margin: "-10%" }}
                className="relative"
              >
                {/* Mobile layout: stacked vertically, Desktop layout: alternating sides */}
                <div className={`flex flex-col items-center text-center md:flex ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'} md:items-center`}>


                  {/* Content side - full width on mobile, half width on desktop */}
                  <motion.div
                    className="w-full mb-4 md:w-1/2 md:mb-0"
                    initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{
                      duration: 0.8,
                      delay: 0.3,
                      type: "spring",
                      stiffness: 80,
                      damping: 18
                    }}
                    viewport={{ once: true }}
                  >
                    <div className="group relative bg-mono-surface/[0.08] border border-mono-border/50 rounded-lg p-4 text-center md:rounded-xl md:p-6 lg:rounded-2xl lg:p-8 backdrop-blur-xl hover:bg-mono-surface/[0.15] hover:border-mono-border transition-all duration-500">
                      {/* Card glow effect */}
                      <div
                        className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-500 blur-xl"
                        style={{ backgroundColor: category.color }}
                      />

                      <div className="relative z-10">
                        {/* Icon inside card for mobile breakpoints */}
                        <div className="flex justify-center mb-3 md:hidden">
                          <motion.div
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{
                              duration: 0.8,
                              delay: 0.5,
                              type: "spring",
                              stiffness: 80,
                              damping: 18
                            }}
                            viewport={{ once: true }}
                            className="relative flex justify-center"
                          >
                            {/* Plain icon with optimized glow effect */}
                            <div className="relative">
                              {/* Subtle glow effect - only around the icon */}
                              <motion.div
                                className="absolute inset-0 rounded-full blur-[8px]"
                                style={{
                                  backgroundColor: category.color,
                                  width: '100%',
                                  height: '100%'
                                }}
                                animate={{
                                  opacity: [0.2, 0.3, 0.2],
                                  scale: [1, 1.1, 1]
                                }}
                                transition={{
                                  duration: 3,
                                  repeat: Infinity,
                                  ease: "easeInOut"
                                }}
                              />

                              {/* Icon */}
                              <motion.div
                                animate={{
                                  scale: [1, 1.05, 1],
                                  rotate: [0, 5, 0, -5, 0]
                                }}
                                transition={{
                                  duration: 6,
                                  repeat: Infinity,
                                  ease: "easeInOut"
                                }}
                                className="relative z-10"
                              >
                                {React.createElement(category.icon, {
                                  className: "w-10 h-10 sm:w-12 sm:h-12",
                                  style: {
                                    color: category.color,
                                    filter: `drop-shadow(0 0 8px ${category.color}60)`
                                  }
                                })}
                              </motion.div>
                            </div>
                          </motion.div>
                        </div>

                        <h3
                          className="text-lg mb-2 md:text-xl md:mb-3 lg:text-2xl lg:mb-4 font-bold group-hover:scale-105 transition-transform duration-300"
                          style={{ color: category.color }}
                        >
                          {category.title}
                        </h3>
                        <p className="text-mono-secondary text-sm mb-3 leading-relaxed md:mb-4 lg:text-base lg:mb-6">
                          {category.description}
                        </p>
                        <div className="grid grid-cols-3 gap-2 md:gap-3 lg:gap-4">
                          {category.stats.map((stat, idx) => (
                            <motion.div
                              key={idx}
                              className="text-center p-2 rounded-md md:rounded-md lg:p-3 lg:rounded-lg bg-mono-surface/[0.05] border border-mono-border/30"
                              whileHover={{ scale: 1.05 }}
                              transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                              <div
                                className="text-lg mb-0.5 md:text-xl md:mb-1 lg:text-2xl lg:mb-1 font-bold"
                                style={{ color: category.color }}
                              >
                                {stat.value}
                              </div>
                              <div className="text-xs text-mono-secondary uppercase tracking-wide">
                                {stat.label}
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  {/* Icon side - hidden on mobile, positioned on desktop */}
                  <div className="relative w-full hidden md:flex md:w-1/2 md:justify-center">


                    {/* Icon display */}
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8, x: index % 2 === 0 ? 30 : -30 }}
                      whileInView={{ opacity: 1, scale: 1, x: 0 }}
                      transition={{
                        duration: 0.8,
                        delay: 0.5,
                        type: "spring",
                        stiffness: 80,
                        damping: 18
                      }}
                      viewport={{ once: true }}
                      className="relative"
                    >
                      {/* Plain icon with optimized glow effect */}
                      <div className="relative">
                        {/* Subtle glow effect - only around the icon */}
                        <motion.div
                          className="absolute inset-0 rounded-full blur-[24px] lg:blur-[28px]"
                          style={{
                            backgroundColor: category.color,
                            width: '100%',
                            height: '100%'
                          }}
                          animate={{
                            opacity: [0.2, 0.3, 0.2],
                            scale: [1, 1.1, 1]
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        />

                        {/* Icon */}
                        <motion.div
                          animate={{
                            scale: [1, 1.05, 1],
                            rotate: [0, 5, 0, -5, 0]
                          }}
                          transition={{
                            duration: 6,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                          className="relative z-10"
                        >
                          {React.createElement(category.icon, {
                            className: "w-20 h-20 lg:w-24 lg:h-24",
                            style: {
                              color: category.color,
                              filter: `drop-shadow(0 0 12px ${category.color}60)`
                            }
                          })}
                        </motion.div>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
} 