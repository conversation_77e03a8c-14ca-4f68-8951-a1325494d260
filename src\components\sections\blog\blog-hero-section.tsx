'use client'
import React from 'react'
import { motion } from 'framer-motion'
import { ArrowRight, BookOpen, Calendar, TrendingUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ScrollLink } from '@/components/ui/scroll-link'

// Animation variants following the established pattern
const transitionVariants = {
  item: {
    hidden: {
      opacity: 0,
      filter: 'blur(12px)',
      y: 12,
    },
    visible: {
      opacity: 1,
      filter: 'blur(0px)',
      y: 0,
      transition: {
        type: 'spring' as const,
        bounce: 0.3,
        duration: 1.5,
      },
    },
  },
}

// Blog stats data
const blogStats = [
  {
    value: '25+',
    label: 'Articles Published',
    description: 'In-depth technical content',
    color: '#00FF85', // Neon green
    icon: <BookOpen className="w-5 h-5" />
  },
  {
    value: '10K+',
    label: 'Monthly Readers',
    description: 'Growing developer community',
    color: '#1E90FF', // Electric blue
    icon: <TrendingUp className="w-5 h-5" />
  },
  {
    value: '3+',
    label: 'Years Writing',
    description: 'Sharing knowledge & insights',
    color: '#FF0099', // Vivid pink
    icon: <Calendar className="w-5 h-5" />
  }
]

export function BlogHeroSection() {
  return (
    <section className="relative bg-mono-bg min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-mono-bg via-mono-surface-dark/20 to-mono-bg" />
      
      <div className="relative max-w-7xl mx-auto px-6 py-24 lg:py-32">
        <div className="text-center space-y-12">
          {/* Main heading */}
          <motion.div
            variants={transitionVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            <motion.p 
              className="text-mono-secondary text-sm tracking-wide uppercase font-medium"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              DEVELOPMENT INSIGHTS
            </motion.p>
            
            <motion.h1 
              className="text-4xl md:text-6xl lg:text-7xl font-bold text-mono-text leading-tight tracking-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Crafting Digital{' '}
              <span className="italic font-cursive text-transparent bg-clip-text bg-gradient-to-r from-accent-neon to-accent-electric">
                Knowledge
              </span>
            </motion.h1>
            
            <motion.p 
              className="text-xl md:text-2xl text-mono-secondary max-w-4xl mx-auto leading-relaxed font-light"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Sharing insights, tutorials, and thoughts on modern web development, 
              programming best practices, and emerging technologies that shape our digital future
            </motion.p>
          </motion.div>

          {/* Blog stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
          >
            {blogStats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                className="bg-mono-surface/[0.08] border border-mono-border rounded-2xl p-6 backdrop-blur-xl hover:bg-mono-surface/[0.12] transition-all duration-300"
              >
                <div className="flex items-center justify-center mb-4">
                  <div 
                    className="p-3 rounded-xl"
                    style={{ backgroundColor: `${stat.color}20`, color: stat.color }}
                  >
                    {stat.icon}
                  </div>
                </div>
                <div 
                  className="text-3xl font-bold mb-2"
                  style={{ color: stat.color }}
                >
                  {stat.value}
                </div>
                <h3 className="text-mono-text font-semibold mb-1">
                  {stat.label}
                </h3>
                <p className="text-mono-secondary text-sm">
                  {stat.description}
                </p>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="flex flex-col sm:flex-row items-center justify-center gap-4"
          >
            <Button 
              asChild
              className="bg-gradient-to-r from-accent-neon to-accent-electric text-black font-semibold hover:from-accent-neon/90 hover:to-accent-electric/90 transition-all duration-300 px-8 py-3 h-auto"
            >
              <ScrollLink to="#featured" className="flex items-center gap-2">
                Explore Articles
                <ArrowRight className="w-4 h-4" />
              </ScrollLink>
            </Button>
            
            <Button 
              variant="outline"
              asChild
              className="border-mono-border hover:bg-mono-surface/20 px-8 py-3 h-auto"
            >
              <ScrollLink to="#newsletter">
                Subscribe to Newsletter
              </ScrollLink>
            </Button>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
