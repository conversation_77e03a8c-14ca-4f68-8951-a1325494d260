/**
 * Enhanced Link component with automatic scroll-to-top functionality
 * Provides smooth navigation experience across the application
 */

import React from 'react'
import { Link, LinkProps } from 'react-router-dom'
import { scrollToTopDelayed, ScrollToTopOptions } from '@/lib/scroll-to-top'

interface ScrollLinkProps extends LinkProps {
  scrollOptions?: ScrollToTopOptions
  scrollDelay?: number
  children: React.ReactNode
}

/**
 * Link component that automatically scrolls to top when navigating
 * @param scrollOptions - Scroll behavior options
 * @param scrollDelay - Delay before scrolling (default: 100ms)
 * @param props - Standard Link props
 */
export const ScrollLink: React.FC<ScrollLinkProps> = ({
  scrollOptions = { behavior: 'smooth' },
  scrollDelay = 100,
  onClick,
  children,
  ...props
}) => {
  const handleClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
    // Execute original onClick if provided
    if (onClick) {
      onClick(event)
    }

    // Don't scroll to top if navigation is prevented
    if (event.defaultPrevented) {
      return
    }

    // Scroll to top with delay to allow route transition
    scrollToTopDelayed(scrollDelay, scrollOptions)
  }

  return (
    <Link onClick={handleClick} {...props}>
      {children}
    </Link>
  )
}

/**
 * Hook to create a scroll-enabled link handler
 * Useful for custom navigation implementations
 */
export const useScrollLink = (
  scrollOptions: ScrollToTopOptions = { behavior: 'smooth' },
  scrollDelay: number = 100
) => {
  return (originalHandler?: (event: React.MouseEvent) => void) => {
    return (event: React.MouseEvent) => {
      // Execute original handler if provided
      if (originalHandler) {
        originalHandler(event)
      }

      // Don't scroll if navigation is prevented
      if (event.defaultPrevented) {
        return
      }

      // Scroll to top with delay
      scrollToTopDelayed(scrollDelay, scrollOptions)
    }
  }
}
