import { useState, useEffect } from 'react';
// import { useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Logo } from './logo';
import { NavLinks } from './navlinks';
import { NavActions } from './navactions';

export function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20); // Reduced threshold for earlier activation
    };
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header role="banner">
      <nav
        className="fixed group z-50 w-full px-2"
        role="navigation"
        aria-label="Main navigation"
      >
        <div className={cn(
          'mx-auto mt-4 max-w-6xl px-6 transition-all duration-500 ease-out lg:px-12',
          // Mobile glassmorphism effects for <425px breakpoint
          isScrolled && 'max-[424px]:bg-black/30 max-[424px]:backdrop-blur-md max-[424px]:rounded-2xl max-[424px]:shadow-lg',
          // Mobile glassmorphism effects for 425px-639px breakpoint
          isScrolled && 'mobile:bg-black/30 mobile:backdrop-blur-md mobile:rounded-2xl mobile:shadow-lg max-sm-tablet:bg-black/30 max-sm-tablet:backdrop-blur-md max-sm-tablet:rounded-2xl max-sm-tablet:shadow-lg',
          // Mobile glassmorphism effects for 640px-767px breakpoint
          isScrolled && 'sm-tablet:bg-black/30 sm-tablet:backdrop-blur-md sm-tablet:rounded-2xl sm-tablet:shadow-lg max-tablet:bg-black/30 max-tablet:backdrop-blur-md max-tablet:rounded-2xl max-tablet:shadow-lg',
          // Transparent navigation for tablet breakpoints (768px-1023px)
          isScrolled && 'laptop-sm:bg-black/40 max-w-4xl laptop-sm:rounded-2xl laptop-sm:backdrop-blur-2xl laptop-sm:border laptop-sm:border-white/10 laptop-sm:shadow-xl lg:px-5',
          isScrolled && 'laptop-sm:bg-gradient-to-br laptop-sm:from-black/50 laptop-sm:via-black/40 laptop-sm:to-black/30'
        )}>
          <div className="relative flex flex-wrap items-center justify-between gap-6 py-3 lg:gap-0 lg:py-4">
            <div className="flex w-full justify-between lg:w-auto">
              <Logo />

              {/* Command Palette Trigger for Mobile */}
              <div className="lg:hidden">
                <NavActions isScrolled={isScrolled} />
              </div>
            </div>

            <NavLinks />

            {/* Command Palette Trigger for Desktop */}
            <div className="hidden lg:block">
              <NavActions isScrolled={isScrolled} />
            </div>
          </div>
        </div>
      </nav>
    </header>
  );
}