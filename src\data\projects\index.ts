/**
 * Projects Data Module
 * Centralized exports for project data, types, and utilities
 * 
 * This module provides a clean interface for accessing all project-related
 * functionality throughout the application.
 */

// Import types for internal use
import type {
  Project,
  ProjectFilters,
  ProjectSortOptions
} from './types'

// Export all types
export type {
  Project,
  FeaturedProject,
  ProjectCategory,
  ProjectStatus,
  TechCategory,
  ProjectFilters,
  ProjectSortOptions,
  ProjectSortBy,
  ProjectSortOrder,
  ProjectStats
} from './types'

// Export constants and configurations
export {
  PROJECT_CATEGORIES,
  PROJECT_STATUS,
  TECHNOLOGY_CATEGORIES,
  DEFAULT_CARD_STYLES,
  FEATURED_PROJECTS_CONFIG,
  VALIDATION_RULES,
  DEFAULT_PROJECT_TEMPLATE,
  SORT_CONFIGURATIONS
} from './constants'

// Export project data
export {
  projects,
  nexustore,
  weatherdash,
  taskflow,
  portfolioWebsite,
  nathanielsEventDecor,
  watchindex,
  PROJECT_METADATA
} from './projects-data'

// Export utility functions
export {
  getAllProjects,
  getFeaturedProjects,
  getProjectById,
  filterProjects,
  sortProjects,
  getProjectStats,
  getAllTechnologies,
  getAllCategories
} from './utils'

// Export type guards
export { isProject } from './types'

/**
 * Quick access to commonly used functions
 */
export const ProjectsAPI = {
  // Data access
  getAll: () => import('./utils').then(m => m.getAllProjects()),
  getFeatured: () => import('./utils').then(m => m.getFeaturedProjects()),
  getById: (id: string) => import('./utils').then(m => m.getProjectById(id)),
  
  // Statistics
  getStats: () => import('./utils').then(m => m.getProjectStats()),
  getTechnologies: () => import('./utils').then(m => m.getAllTechnologies()),
  getCategories: () => import('./utils').then(m => m.getAllCategories()),
  
  // Filtering and sorting
  filter: (filters: ProjectFilters) => import('./utils').then(m => m.filterProjects(filters)),
  sort: (projects: Project[], options: ProjectSortOptions) => import('./utils').then(m => m.sortProjects(projects, options))
} as const
