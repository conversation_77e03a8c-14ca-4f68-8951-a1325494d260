import React, { useRef, useState } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';
import { MapPin, Code2, Heart } from 'lucide-react';
import { sectionConfigs } from '@/styles/spacing';
interface ButtonItemProps {
  icon: React.ElementType;
  text: string;
  color: string;
}

const ButtonItem: React.FC<ButtonItemProps> = ({ icon: Icon, text, color }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      className="flex-1 relative flex items-center justify-center gap-2 px-4 py-3 sm:px-3 sm:py-2 lg:px-4 lg:py-3 bg-mono-surface-dark/10 backdrop-blur-sm border border-mono-border/30 rounded-lg shadow-2xl overflow-hidden"
      initial={{ scale: 1, y: 0 }}
      animate={{
        scale: isHovered ? 1.02 : 1,
        y: isHovered ? -2 : 0,
      }}
      transition={{
        type: "spring",
        stiffness: 120,
        damping: 18,
        mass: 0.8
      }}
      whileHover={{
        borderColor: color + '40',
        transition: { duration: 0.3 }
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Subtle hover glow effect */}
      <motion.div
        className="absolute inset-0 opacity-0"
        style={{ backgroundColor: color }}
        animate={{
          opacity: isHovered ? 0.03 : 0
        }}
        transition={{ duration: 0.3 }}
      />
      {/* Icon with refined glow effect */}
      <div className="relative inline-flex items-center justify-center">
        <motion.div
          className="relative inline-flex items-center justify-center"
          initial={{ rotate: 0, scale: 1 }}
          animate={{
            rotate: isHovered ? 10 : 0, // Reduced rotation
            scale: isHovered ? 1.05 : 1  // Reduced scale
          }}
          transition={{
            duration: 0.3,
            ease: "easeOut",
            type: "spring",
            stiffness: 120,
            damping: 18
          }}
        >
          <Icon
            className="w-4 h-4 sm:w-3.5 sm:h-3.5 lg:w-4 lg:h-4 relative z-10 flex-shrink-0"
            style={{
              color: color,
              filter: `drop-shadow(0 0 8px ${color}40)`
            }}
          />
          {/* Icon glow effect */}
          <motion.div
            className="absolute w-4 h-4 blur-xl -z-10 rounded-full"
            style={{ backgroundColor: color }}
            initial={{ opacity: 0.15, scale: 1 }}
            animate={{
              opacity: isHovered ? 0.25 : 0.15, // Reduced opacity change
              scale: isHovered ? 1.1 : 1        // Reduced scale
            }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          />
        </motion.div>
      </div>

      <span className="text-mono-text font-medium text-sm sm:text-xs lg:text-sm leading-tight whitespace-nowrap text-center">
        {text}
      </span>

      {/* Subtle hover glow effect */}
      <motion.div
        className="absolute inset-0 rounded-xl"
        style={{ backgroundColor: color }}
        initial={{ opacity: 0, scale: 1 }}
        animate={{
          opacity: isHovered ? 0.015 : 0, // More subtle glow
          scale: isHovered ? 1.01 : 1     // Reduced scale
        }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      />
    </motion.div>
  );
};

export function AboutHeroSection() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isProfileHovered, setIsProfileHovered] = useState(false);

  // Enhanced scroll-based animations with smoother transitions
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start 0.95", "end 0.05"] // Adjusted for smoother transition
  })

  // Refined scroll-based animations
  const backgroundY1 = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, -15]), // Reduced movement
    { stiffness: 80, damping: 20, restDelta: 0.0005 } // Smoother spring
  )

  const backgroundY2 = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, 15]), // Reduced movement
    { stiffness: 80, damping: 20, restDelta: 0.0005 } // Smoother spring
  )

  // Subtle content fade effect on scroll
  const contentOpacity = useTransform(
    scrollYProgress, 
    [0, 0.2, 0.8, 1], 
    [1, 1, 0.9, 0.8]
  )

  return (
    <section ref={containerRef} className="relative bg-mono-bg pt-36 pb-20 overflow-hidden">
      {/* Enhanced background effects with more layers */}
      <div className="absolute inset-0">
        {/* Primary animated blob */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-[800px] h-[800px] bg-mono-surface/8 rounded-full blur-[150px]"
          style={{
            y: backgroundY1,
            willChange: 'transform'
          }}
          initial={{ opacity: 0.07, scale: 1 }}
          animate={{
            opacity: [0.07, 0.1, 0.07],
            scale: [1, 1.03, 1],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />

        {/* Secondary animated blob */}
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-[1000px] h-[1000px] bg-mono-surface-light/8 rounded-full blur-[180px]"
          style={{
            y: backgroundY2,
            willChange: 'transform'
          }}
          initial={{ opacity: 0.05, scale: 1.05 }}
          animate={{
            opacity: [0.05, 0.08, 0.05],
            scale: [1.05, 1.02, 1.05],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />

        {/* Additional accent blob for depth */}
        <motion.div
          className="absolute top-1/2 left-1/2 w-[600px] h-[600px] bg-gradient-to-r from-[#CF6FF4]/5 to-[#8B31CD]/5 rounded-full blur-[120px] -translate-x-1/2 -translate-y-1/2"
          animate={{
            opacity: [0.03, 0.06, 0.03],
            scale: [1, 1.05, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 40,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />
      </div>

      {/* Enhanced section header with premium styling - UNIFIED SPACING */}
      <div className={`relative z-10 ${sectionConfigs.aboutHero.container} lg:max-w-4xl mb-8`}>
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.7,
            type: 'spring',
            stiffness: 80,
            damping: 18
          }}
          viewport={{ once: true, margin: "-10%" }}
          className="space-y-4 text-center"
        >
          <motion.p
            className="text-mono-accent text-sm font-medium tracking-widest uppercase"
            initial={{ opacity: 0, y: 5 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            About Me
          </motion.p>

          <motion.h2
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight tracking-tight"
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Hello{' '}
            <span className="relative inline-block">
              <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                World
              </span>
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-15"
                initial={{ opacity: 0.15 }}
                animate={{
                  opacity: [0.15, 0.25, 0.15],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
              />
            </span>
          </motion.h2>

          {/* Subtle decorative line */}
          <motion.div
            className="w-24 h-0.5 bg-gradient-to-r from-transparent via-mono-accent/30 to-transparent mx-auto"
            initial={{ scaleX: 0, opacity: 0 }}
            whileInView={{ scaleX: 1, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          />
        </motion.div>
      </div>

      <motion.div
        className={`relative z-10 ${sectionConfigs.aboutHero.container} lg:max-w-4xl`}
        style={{ opacity: contentOpacity }} // Subtle fade on scroll
      >
        {/* Hero Content - UNIFIED SPACING */}
        <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-20 items-center">
          {/* Profile Card - Now First on Tablet */}
          <motion.div
            initial={{ opacity: 0, x: 20 }} // Reduced initial offset
            whileInView={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.6,
              delay: 0.3,
              type: "spring",
              stiffness: 80,
              damping: 18
            }}
            viewport={{ once: true, margin: "-10%" }}
            className="relative flex items-start justify-center md:order-1 lg:order-2"
          >
            <motion.div
              className={`relative bg-mono-surface-dark/10 backdrop-blur-sm border border-mono-border/30 rounded-2xl p-6 lg:p-8 shadow-2xl ${sectionConfigs.aboutHero.profileCard} overflow-hidden`}
              initial={{ scale: 1, y: 0 }}
              animate={{
                scale: isProfileHovered ? 1.01 : 1,
                y: isProfileHovered ? -2 : 0,
              }}
              transition={{
                type: "spring",
                stiffness: 120,
                damping: 18,
                mass: 0.8
              }}
              whileHover={{
                borderColor: '#CF6FF440',
                transition: { duration: 0.3 }
              }}
              onMouseEnter={() => setIsProfileHovered(true)}
              onMouseLeave={() => setIsProfileHovered(false)}
            >
              {/* Premium card background glow */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-[#CF6FF4]/5 via-transparent to-[#8B31CD]/5 opacity-0"
                animate={{
                  opacity: isProfileHovered ? 1 : 0
                }}
                transition={{ duration: 0.3 }}
              />

              {/* Profile Image with refined styling */}
              <motion.div
                className="relative z-20 mb-6"
                initial={{ scale: 1 }}
                animate={{
                  scale: isProfileHovered ? 1.01 : 1, // Reduced scale
                }}
                transition={{
                  duration: 0.3,
                  ease: "easeOut",
                  type: "spring",
                  stiffness: 120,
                  damping: 18
                }}
              >
                <div className="relative rounded-2xl overflow-hidden shadow-2xl ring-1 ring-white/5">
                  <img
                    src="/about_profile.jpg"
                    alt="CJ Jutba - Frontend Developer"
                    className="w-full h-56 object-cover object-center"
                    loading="lazy"
                  />
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"
                    initial={{ opacity: 0 }}
                    animate={{
                      opacity: isProfileHovered ? 0.7 : 0 // Enhanced gradient for better visibility
                    }}
                    transition={{ duration: 0.2 }}
                  />
                </div>
              </motion.div>

              {/* Enhanced profile content */}
              <div className="relative z-10 text-center space-y-3">
                <motion.h3
                  className="text-2xl font-bold text-mono-text tracking-tight leading-tight"
                  initial={{ scale: 1, y: 0 }}
                  animate={{
                    scale: isProfileHovered ? 1.005 : 1, // More subtle scale
                    y: isProfileHovered ? -0.5 : 0       // More subtle movement
                  }}
                  transition={{ duration: 0.2, ease: "easeOut" }}
                >
                  CJ Jutba
                </motion.h3>

                <motion.div
                  className="space-y-2"
                  initial={{ opacity: 0, y: 8 }} // Reduced initial offset
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.7,
                    type: 'spring',
                    stiffness: 80,
                    damping: 18,
                    delay: 0.2
                  }}
                  viewport={{ once: true }}
                >
                  <h4 className="text-lg font-semibold bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text">
                    Frontend Developer
                  </h4>
                  <div className="w-16 h-0.5 bg-gradient-to-r from-[#CF6FF4] to-[#8B31CD] mx-auto rounded-full opacity-60"></div>
                </motion.div>
              </div>

              {/* Subtle hover glow effect */}
              <motion.div
                className="absolute inset-0 rounded-2xl"
                style={{ backgroundColor: '#CF6FF4' }}
                initial={{ opacity: 0, scale: 1 }}
                animate={{
                  opacity: isProfileHovered ? 0.015 : 0, // More subtle glow
                  scale: isProfileHovered ? 1.01 : 1     // Reduced scale
                }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              />
            </motion.div>

            {/* Refined floating shadow */}
            <motion.div
              className="absolute inset-0 bg-mono-surface-dark/15 rounded-3xl blur-2xl -z-10"
              initial={{ scale: 1, opacity: 0.15 }}
              animate={{
                scale: isProfileHovered ? 1.03 : 1,     // Reduced scale
                opacity: isProfileHovered ? 0.2 : 0.15  // More subtle opacity change
              }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            />
          </motion.div>

          {/* Text Content - Now Second on Tablet */}
          <motion.div
            initial={{ opacity: 0, x: -20 }} // Reduced initial offset
            whileInView={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.6,
              delay: 0.2,
              type: "spring",
              stiffness: 80,
              damping: 18
            }}
            viewport={{ once: true, margin: "-10%" }}
            className="space-y-6 lg:space-y-8 md:order-2 lg:order-1"
          >

            <motion.div
              className={`space-y-4 lg:space-y-6 ${sectionConfigs.aboutHero.textContent}`}
              initial={{ opacity: 0, y: 10 }} // Reduced initial offset
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.7,
                type: 'spring',
                stiffness: 80,
                damping: 18,
                delay: 0.2
              }}
              viewport={{ once: true, margin: "-10%" }}
            >
              <p className="text-mono-secondary leading-relaxed tracking-wide font-light text-sm lg:text-base text-center lg:text-left">
                I'm a passionate frontend developer who creates modern, responsive web applications.
                I specialize in React, TypeScript, and modern CSS frameworks to build user interfaces
                that are both beautiful and functional. My journey in frontend development is driven
                by a love for clean code and exceptional user experiences.
              </p>
            </motion.div>

            <motion.div
              className={`flex flex-col sm:flex-row gap-3 sm:gap-2 lg:gap-3 ${sectionConfigs.aboutHero.buttonContainer} justify-center lg:justify-start`}
              initial={{ opacity: 0, y: 8 }} // Reduced initial offset
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.4,
                ease: "easeOut",
                delay: 0.3
              }}
              viewport={{ once: true, margin: "-10%" }}
            >
              {[
                { icon: MapPin, text: 'Bato, Plaridel', color: '#00FF85' },
                { icon: Code2, text: 'Frontend Developer', color: '#1E90FF' },
                { icon: Heart, text: 'UI/UX Enthusiast', color: '#FF0099' }
              ].map((item) => (
                <ButtonItem
                  key={item.text}
                  icon={item.icon}
                  text={item.text}
                  color={item.color}
                />
              ))}
            </motion.div>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
}