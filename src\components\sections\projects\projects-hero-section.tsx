// import React from 'react';
import { motion } from 'framer-motion';
import { useProjectStatCards } from '@/hooks/use-project-stats';

export function ProjectsHeroSection() {
  // Get dynamic project statistics
  const projectStats = useProjectStatCards();

  return (
    <section className="relative bg-mono-bg pt-36 pb-20 overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute top-1/3 left-1/4 w-[500px] h-[500px] bg-mono-surface/3 rounded-full blur-[100px]"
          initial={{ opacity: 0 }}
          animate={{
            y: [-8, 8],
            x: [-4, 4],
            opacity: [0.03, 0.06, 0.03],
            transition: {
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }
          }}
        />
        <motion.div
          className="absolute bottom-1/3 right-1/4 w-[600px] h-[600px] bg-mono-accent/2 rounded-full blur-[120px]"
          initial={{ opacity: 0 }}
          animate={{
            y: [8, -8],
            x: [4, -4],
            opacity: [0.02, 0.05, 0.02],
            transition: {
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }
          }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[400px] h-[400px] bg-gradient-to-r from-[#CF6FF4]/2 to-[#8B31CD]/2 rounded-full blur-[80px]"
          initial={{ opacity: 0 }}
          animate={{
            scale: [0.8, 1.1, 0.8],
            opacity: [0.01, 0.03, 0.01],
            transition: {
              duration: 12,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }
          }}
        />
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-8 max-[424px]:px-8 mobile:px-8 sm:px-8 md:px-8 lg:px-6 max-[424px]:max-w-4xl mobile:max-w-4xl">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-10%" }}
          className="text-center mb-8"
        >
          <p className="text-mono-accent text-sm font-medium tracking-widest uppercase mb-3">
            Project Portfolio
          </p>
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight tracking-tight mb-6">
            Frontend{' '}
            <span className="relative inline-block">
              <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                Projects
              </span>
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-15"
                initial={{ opacity: 0.15 }}
                animate={{
                  opacity: [0.15, 0.25, 0.15],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                aria-hidden="true"
              />
            </span>
          </h1>
          <p className="text-mono-secondary text-sm lg:text-base max-w-3xl mx-auto leading-relaxed">
            A collection of my frontend development work, showcasing modern web applications built with React, TypeScript, and cutting-edge technologies
          </p>
        </motion.div>

        {/* Project stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 max-w-4xl mx-auto">
          {projectStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                duration: 0.6,
                delay: index * 0.15,
                type: "spring",
                stiffness: 100,
                damping: 20
              }}
              viewport={{ once: true, margin: "-50px" }}
              whileHover={{
                y: -4,
                transition: { type: "spring", stiffness: 400, damping: 25 }
              }}
              className="group text-center bg-mono-surface/[0.08] border border-mono-border rounded-xl p-4 lg:rounded-2xl lg:p-6 backdrop-blur-xl hover:bg-mono-surface/[0.12] hover:border-mono-accent/20 transition-all duration-300"
            >
              <div className="text-2xl lg:text-3xl font-bold text-mono-text mb-2 group-hover:text-mono-accent transition-colors duration-300">
                {stat.value}
              </div>
              <div className="text-mono-text text-sm lg:text-base font-semibold mb-1">
                {stat.label}
              </div>
              <div className="text-mono-secondary text-xs lg:text-sm">
                {stat.description}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
} 