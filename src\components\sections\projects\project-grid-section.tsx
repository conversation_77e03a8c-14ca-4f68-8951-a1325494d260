import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ExternalLink, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { getAllProjects, type Project } from '@/data/projects';

// Get all projects from centralized data, sorted by priority (highest first)
const projects = [...getAllProjects()].sort((a: Project, b: Project) => b.priority - a.priority);

// Pagination configuration
const PROJECTS_PER_PAGE = 3; // Set to 3 to show pagination with current 4 projects. Change to 6 when you have 7+ projects

export function ProjectGridSection() {
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Calculate pagination values
  const totalPages = Math.ceil(projects.length / PROJECTS_PER_PAGE);
  const startIndex = (currentPage - 1) * PROJECTS_PER_PAGE;
  const endIndex = startIndex + PROJECTS_PER_PAGE;
  const currentProjects = projects.slice(startIndex, endIndex);

  // Pagination handlers
  const goToPage = (page: number) => {
    if (page === currentPage) return;

    setIsTransitioning(true);
    setCurrentPage(page);

    // Smooth scroll to top of section
    const section = document.querySelector('[data-section="project-grid"]');
    if (section) {
      section.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    // Reset transition state after animation
    setTimeout(() => setIsTransitioning(false), 300);
  };

  const goToPrevious = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const goToNext = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  return (
    <section data-section="project-grid" className="relative bg-mono-bg overflow-hidden py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32">
      {/* Subtle background effects */}
      <div className="absolute inset-0" aria-hidden="true">
        <motion.div
          className="absolute top-1/3 left-1/4 w-[500px] h-[500px] bg-mono-surface/4 rounded-full blur-[100px] will-change-transform"
          animate={{
            scale: [1, 1.05, 1],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-1/3 right-1/4 w-[600px] h-[600px] bg-mono-surface-light/4 rounded-full blur-[120px] will-change-transform"
          animate={{
            scale: [1.05, 1, 1.05],
            opacity: [0.15, 0.25, 0.15],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 max-[424px]:px-8 mobile:px-8"> {/* Match beyond-the-code section content width */}
        {/* Projects Grid - Optimized spacing */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentPage}
            className={`grid gap-4 max-[424px]:gap-5 mobile:gap-5 sm:gap-4 md:gap-5 lg:gap-6 grid-cols-1 max-[424px]:grid-cols-1 mobile:grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 transition-opacity duration-300 ${
              isTransitioning ? 'opacity-75' : 'opacity-100'
            }`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {currentProjects.map((project, index) => (
            <motion.div
              key={project.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -8, transition: { duration: 0.2, ease: "easeOut" } }}
              className="group relative bg-gradient-to-br from-[#0F0F0F] to-[#1A1A1A] border border-mono-border/20 rounded-xl max-[424px]:rounded-xl mobile:rounded-xl sm:rounded-xl md:rounded-xl lg:rounded-2xl p-4 max-[424px]:p-4 mobile:p-4 sm:p-4 md:p-4 lg:p-5 backdrop-blur-sm hover:border-mono-border/40 hover:shadow-xl hover:shadow-black/20 transition-all duration-200 transform-gpu will-change-transform"
            >
              {/* Status Badge - Positioned outside image for better visibility */}
              <div className="flex justify-between items-start mb-2 max-[424px]:mb-2.5 mobile:mb-2.5 sm:mb-2.5 md:mb-2.5 lg:mb-3">
                <span className={`inline-flex items-center px-2.5 py-1 max-[424px]:px-2.5 max-[424px]:py-1 mobile:px-2.5 mobile:py-1 sm:px-2.5 sm:py-1 md:px-2.5 md:py-1 lg:px-3 lg:py-1.5 rounded-full text-xs font-semibold shadow-lg ${
                  project.status === 'Completed'
                    ? 'bg-green-500/90 text-white border border-green-400/50'
                    : 'bg-amber-500/90 text-white border border-amber-400/50'
                }`}>
                  <div className={`w-1.5 h-1.5 rounded-full mr-2 ${
                    project.status === 'Completed' ? 'bg-green-200' : 'bg-amber-200'
                  }`} />
                  {project.status}
                </span>
              </div>

              {/* Project Image - Reduced height for mobile */}
              <div className="relative mb-3 max-[424px]:mb-3 mobile:mb-3 sm:mb-3 md:mb-3 lg:mb-4 rounded-lg max-[424px]:rounded-lg mobile:rounded-lg sm:rounded-lg md:rounded-lg lg:rounded-xl overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-32 max-[424px]:h-32 mobile:h-32 sm:h-28 md:h-32 lg:h-36 object-cover transition-transform duration-200 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-black/10 to-transparent" />

                {/* Enhanced Hover Overlay - Instant response */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0"
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.15 }}
                />
              </div>

              {/* Project Content - Reduced spacing for mobile */}
              <div className="space-y-3 max-[424px]:space-y-3 mobile:space-y-3 sm:space-y-3 md:space-y-3 lg:space-y-4">
                <div>
                  <h3 className="text-base max-[424px]:text-base mobile:text-base sm:text-base md:text-base lg:text-lg font-bold text-mono-text leading-tight mb-1.5 max-[424px]:mb-1.5 mobile:mb-1.5 sm:mb-1.5 md:mb-1.5 lg:mb-2 group-hover:text-white transition-colors duration-200">
                    {project.title}
                  </h3>
                  <p className="text-mono-secondary text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm leading-relaxed line-clamp-2">
                    {project.description}
                  </p>
                </div>

                {/* Technologies - Enhanced styling with instant hover */}
                <div className="flex flex-wrap gap-1 max-[424px]:gap-1.5 mobile:gap-1.5 sm:gap-1 md:gap-1 lg:gap-1.5">
                  {project.technologies.map((tech: string) => (
                    <motion.span
                      key={tech}
                      className="inline-flex items-center px-2 py-0.5 max-[424px]:px-2 max-[424px]:py-0.5 mobile:px-2 mobile:py-0.5 sm:px-2 sm:py-0.5 md:px-2 md:py-0.5 lg:px-2.5 lg:py-1 bg-[#1A1A1A]/60 border border-[#333333]/50 rounded-full text-[#E0E0E0] text-xs font-medium backdrop-blur-sm hover:border-mono-accent/50 hover:text-mono-accent hover:bg-mono-accent/5 transition-all duration-150 cursor-default will-change-transform"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.15 }}
                    >
                      {tech}
                    </motion.span>
                  ))}
                </div>

                {/* Project Highlights - Compact styling for mobile */}
                <div>
                  <h4 className="text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm font-semibold text-mono-text mb-1.5 max-[424px]:mb-1.5 mobile:mb-1.5 sm:mb-1.5 md:mb-1.5 lg:mb-2">Key Features:</h4>
                  <ul className="space-y-1 max-[424px]:space-y-1 mobile:space-y-1 sm:space-y-1 md:space-y-1 lg:space-y-1.5">
                    {project.highlights.map((highlight: string, idx: number) => (
                      <motion.li
                        key={highlight}
                        className="flex items-start gap-2 max-[424px]:gap-2 mobile:gap-2 sm:gap-2 md:gap-2 lg:gap-2.5 text-mono-secondary text-xs"
                        initial={{ opacity: 0, x: -10 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: idx * 0.05, duration: 0.2 }}
                        viewport={{ once: true }}
                      >
                        <div className="w-1.5 h-1.5 bg-mono-accent rounded-full flex-shrink-0 mt-1.5" />
                        <span className="leading-relaxed">{highlight}</span>
                      </motion.li>
                    ))}
                  </ul>
                </div>

                {/* Action Buttons - Optimized for mobile */}
                <div className="flex gap-2 max-[424px]:gap-2 mobile:gap-2 sm:gap-2 md:gap-2 lg:gap-2.5 pt-2 max-[424px]:pt-2.5 mobile:pt-2.5 sm:pt-2.5 md:pt-2.5 lg:pt-3">
                  <Button
                    className="relative group overflow-hidden bg-[#2A2A2A] hover:bg-[#333333] text-white font-medium px-2.5 py-1.5 max-[424px]:px-2.5 max-[424px]:py-1.5 mobile:px-2.5 mobile:py-1.5 sm:px-2.5 sm:py-1.5 md:px-2.5 md:py-1.5 lg:px-3 lg:py-2 rounded-lg max-[424px]:rounded-lg mobile:rounded-lg sm:rounded-lg md:rounded-lg lg:rounded-xl transition-all duration-150 hover:scale-[1.02] border border-[#444444] hover:border-[#555555] will-change-transform focus:ring-2 focus:ring-mono-accent focus:ring-offset-2 focus:ring-offset-mono-bg text-xs flex-1"
                    asChild
                  >
                    <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-150" />
                      <div className="relative flex items-center justify-center">
                        <ExternalLink className="w-3 h-3 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3 sm:h-3 md:w-3 md:h-3 lg:w-3.5 lg:h-3.5 mr-1 max-[424px]:mr-1 mobile:mr-1 sm:mr-1 md:mr-1 lg:mr-1.5" />
                        Live Demo
                      </div>
                    </a>
                  </Button>
                  <Button
                    variant="outline"
                    className="bg-transparent border border-[#333333] text-[#E0E0E0] hover:bg-[#1A1A1A] hover:border-[#444444] px-2.5 py-1.5 max-[424px]:px-2.5 max-[424px]:py-1.5 mobile:px-2.5 mobile:py-1.5 sm:px-2.5 sm:py-1.5 md:px-2.5 md:py-1.5 lg:px-3 lg:py-2 rounded-lg max-[424px]:rounded-lg mobile:rounded-lg sm:rounded-lg md:rounded-lg lg:rounded-xl transition-all duration-150 hover:scale-[1.02] will-change-transform focus:ring-2 focus:ring-mono-accent focus:ring-offset-2 focus:ring-offset-mono-bg text-xs flex-1"
                    asChild
                  >
                    <a href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="w-3 h-3 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3 sm:h-3 md:w-3 md:h-3 lg:w-3.5 lg:h-3.5 mr-1 max-[424px]:mr-1 mobile:mr-1 sm:mr-1 md:mr-1 lg:mr-1.5" />
                      View Code
                    </a>
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
          </motion.div>
        </AnimatePresence>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <motion.div
            className="flex justify-center mt-16 max-[424px]:mt-12 mobile:mt-12 sm:mt-14 md:mt-16 lg:mt-20"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="flex items-center gap-3 max-[424px]:gap-2 mobile:gap-2 sm:gap-3 md:gap-3 lg:gap-4">
              {/* Previous Button */}
              <button
                onClick={goToPrevious}
                disabled={currentPage === 1}
                className="group relative flex items-center justify-center w-10 h-10 max-[424px]:w-8 max-[424px]:h-8 mobile:w-8 mobile:h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 lg:w-11 lg:h-11 rounded-full transition-all duration-200 hover:scale-105 disabled:opacity-40 disabled:cursor-not-allowed disabled:hover:scale-100 will-change-transform hover:bg-[#333333]/80 disabled:hover:bg-transparent"
              >
                <ChevronLeft className="w-4 h-4 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3.5 sm:h-3.5 md:w-4 md:h-4 lg:w-4 lg:h-4 text-mono-text group-hover:text-white transition-colors duration-200" />
              </button>

              {/* Page Numbers */}
              <div className="flex items-center gap-1 max-[424px]:gap-0.5 mobile:gap-0.5 sm:gap-1 md:gap-1 lg:gap-1.5 mx-1 max-[424px]:mx-0.5 mobile:mx-0.5 sm:mx-1 md:mx-1 lg:mx-2">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                  // Show all pages if 7 or fewer, otherwise show smart pagination
                  const shouldShow = totalPages <= 7 ||
                    page === 1 ||
                    page === totalPages ||
                    Math.abs(page - currentPage) <= 1;

                  if (!shouldShow) {
                    // Show ellipsis for gaps
                    if ((page === currentPage - 2 && currentPage > 3) ||
                        (page === currentPage + 2 && currentPage < totalPages - 2)) {
                      return (
                        <span key={`ellipsis-${page}`} className="px-2 text-mono-secondary text-sm font-medium">
                          ...
                        </span>
                      );
                    }
                    return null;
                  }

                  return (
                    <button
                      key={page}
                      onClick={() => goToPage(page)}
                      className={`relative flex items-center justify-center w-10 h-10 max-[424px]:w-8 max-[424px]:h-8 mobile:w-8 mobile:h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 lg:w-11 lg:h-11 rounded-full font-semibold text-sm max-[424px]:text-xs mobile:text-xs sm:text-sm md:text-sm lg:text-sm transition-all duration-200 hover:scale-105 will-change-transform ${
                        currentPage === page
                          ? 'bg-mono-accent text-mono-bg shadow-lg shadow-mono-accent/25 border border-mono-accent/50'
                          : 'bg-[#2A2A2A]/60 hover:bg-[#333333]/80 text-mono-text hover:text-white border border-[#444444]/30 hover:border-[#555555]/50'
                      }`}
                    >
                      <span className="relative z-10">{page}</span>
                      {currentPage === page && (
                        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-full" />
                      )}
                    </button>
                  );
                })}
              </div>

              {/* Next Button */}
              <button
                onClick={goToNext}
                disabled={currentPage === totalPages}
                className="group relative flex items-center justify-center w-10 h-10 max-[424px]:w-8 max-[424px]:h-8 mobile:w-8 mobile:h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 lg:w-11 lg:h-11 rounded-full transition-all duration-200 hover:scale-105 disabled:opacity-40 disabled:cursor-not-allowed disabled:hover:scale-100 will-change-transform hover:bg-[#333333]/80 disabled:hover:bg-transparent"
              >
                <ChevronRight className="w-4 h-4 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-3.5 sm:h-3.5 md:w-4 md:h-4 lg:w-4 lg:h-4 text-mono-text group-hover:text-white transition-colors duration-200" />
              </button>
            </div>
          </motion.div>
        )}
      </div>
    </section>
  );
} 