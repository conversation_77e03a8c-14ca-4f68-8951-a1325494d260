'use client'
import { <PERSON>U<PERSON>, Github, Linkedin, Facebook, Mail } from 'lucide-react'
import { ScrollLink } from '@/components/ui/scroll-link'
import { motion } from 'framer-motion'

export function Footer() {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const footerLinks = {
    navigation: [
      { name: 'About', href: '/about' },
      { name: 'Skills', href: '/skills' },
      { name: 'Projects', href: '/projects' },
      { name: 'Contact', href: '/contact' },
    ],
    social: [
      { name: 'GitHub', href: 'https://github.com/christianjeraldjutba', icon: Github },
      { name: 'LinkedIn', href: 'https://www.linkedin.com/in/christian-jerald-jutba-7a60b236a', icon: Linkedin },
      { name: 'Facebook', href: 'https://www.facebook.com/profile.php?id=61558829783116', icon: Facebook },
      { name: 'Email', href: 'mailto:<EMAIL>', icon: Mail },
    ]
  }

  return (
    <footer className="relative bg-mono-bg border-t border-mono-border/10">
      {/* Premium background effects */}
      <div className="absolute inset-0">
        <div
          className="absolute inset-0 opacity-[0.01]"
          style={{
            backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)',
            backgroundSize: '60px 60px'
          }}
        />
        <div className="absolute top-0 left-1/4 w-[400px] h-[200px] bg-gradient-to-r from-[#CF6FF4]/[0.02] to-[#8B31CD]/[0.01] rounded-full blur-[80px]" />
      </div>

      <div className="relative max-w-7xl mx-auto px-6 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-start">
          {/* Left Column: Brand and Description */}
          <div className="space-y-8">
            <ScrollLink to="/" className="group inline-block">
              <motion.h2
                className="text-3xl font-bold text-mono-text tracking-tight"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                CJ Jutba
              </motion.h2>
            </ScrollLink>
            <p className="text-mono-text/70 text-base max-w-md font-light leading-relaxed">
              Junior frontend developer passionate about creating modern, responsive web applications.
              Learning and growing through hands-on projects with React, TypeScript, and modern web technologies.
            </p>

            {/* Contact Info with premium design */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <span className="text-mono-text/50 text-sm font-medium">Email:</span>
                <a
                  href="mailto:<EMAIL>"
                  className="text-mono-text/80 hover:text-mono-text text-sm transition-colors duration-300 hover:underline decoration-[#CF6FF4]/50"
                >
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-mono-text/50 text-sm font-medium">Based in:</span>
                <span className="text-mono-text/80 text-sm">Plaridel, Misamis Occidental, Philippines</span>
              </div>
            </div>
          </div>

          {/* Right Column: Navigation and Social */}
          <div className="flex flex-col items-start md:items-end gap-8">
            {/* Navigation with premium styling */}
            <nav className="flex flex-wrap justify-start md:justify-end gap-x-8 gap-y-3">
              {footerLinks.navigation.map((link) => (
                <ScrollLink
                  key={link.name}
                  to={link.href}
                  className="text-mono-text/70 hover:text-mono-text text-base font-medium transition-colors duration-300 relative group"
                >
                  {link.name}
                  <motion.div
                    className="absolute -bottom-1 left-0 right-0 h-px bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    initial={false}
                    animate={{ scaleX: 0 }}
                    whileHover={{ scaleX: 1 }}
                  />
                </ScrollLink>
              ))}
            </nav>

            {/* Social Links with premium hover effects */}
            <div className="flex gap-5">
              {footerLinks.social.map((social) => (
                <motion.a
                  key={social.name}
                  href={social.href}
                  target={social.href.startsWith('http') ? '_blank' : undefined}
                  rel={social.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                  className="group relative p-3 rounded-xl transition-all duration-300 overflow-hidden hover:bg-mono-surface/5"
                  aria-label={social.name}
                  whileHover={{ scale: 1.15, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4]/10 via-[#A651E1]/10 to-[#8B31CD]/10 opacity-0 group-hover:opacity-100 transition-all duration-300"
                    initial={false}
                    animate={{ scale: [1, 1.5, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                  <social.icon className="w-5 h-5 text-mono-text/70 group-hover:text-mono-text transition-colors duration-300 relative z-10" />
                </motion.a>
              ))}
            </div>
          </div>
        </div>

        {/* Footer Bottom with premium border */}
        <div className="flex items-center justify-between mt-12 pt-8 border-t border-mono-border/10">
          <p className="text-mono-text/40 text-xs">
            © 2025 Christian Jerald Jutba. All rights reserved.
          </p>
          <motion.button
            onClick={scrollToTop}
            className="group relative p-3 -m-3 text-mono-text/70 hover:text-mono-text transition-colors duration-300 rounded-xl hover:bg-mono-surface/5"
            whileHover={{ scale: 1.15, y: -3 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
            aria-label="Back to top"
          >
            <ArrowUp className="w-5 h-5 relative z-10" />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4]/10 via-[#A651E1]/10 to-[#8B31CD]/10 opacity-0 group-hover:opacity-100 rounded-lg transition-opacity duration-300"
            />
          </motion.button>
        </div>
      </div>
    </footer>
  )
}