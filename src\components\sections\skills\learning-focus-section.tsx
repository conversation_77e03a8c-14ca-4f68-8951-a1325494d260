// import React from 'react';
import { motion } from 'framer-motion';

interface LearningArea {
  title: string;
  description: string;
  progress: number;
  timeframe: string;
  color: string;
}

const learningAreas: LearningArea[] = [
  {
    title: 'React Fundamentals',
    description: 'Getting better with React hooks, state management, and building reusable components',
    progress: 65,
    timeframe: 'Current Focus',
    color: '#00FF85' // Neon green
  },
  {
    title: 'JavaScript Mastery',
    description: 'Strengthening my JavaScript skills with ES6+ features, async programming, and best practices',
    progress: 50,
    timeframe: 'In Progress',
    color: '#1E90FF' // Electric blue
  },
  {
    title: 'Web Accessibility',
    description: 'Learning how to make websites accessible to everyone with proper ARIA labels and semantic HTML',
    progress: 30,
    timeframe: 'Next Goal',
    color: '#FF0099' // Vivid pink
  }
];

export function LearningFocusSection() {
  return (
    <section className="relative bg-mono-bg py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32 overflow-hidden">
      <div className="relative z-10 max-w-6xl mx-auto px-4 max-[424px]:px-8 mobile:px-8 md:px-8 lg:max-w-4xl lg:px-6"> {/* Match featured-projects section content width */}
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-10%" }}
          className="text-center mb-20 max-[424px]:mb-8 mobile:mb-8 sm:mb-12 md:mb-16 lg:mb-20"
        >
          <p className="text-mono-accent text-sm max-[424px]:text-xs mobile:text-xs md:text-xs lg:text-sm font-medium tracking-widest uppercase mb-3 max-[424px]:mb-2 mobile:mb-2 md:mb-2 lg:mb-3">
            Growth Mindset
          </p>
          <h2 className="text-3xl max-[424px]:text-3xl mobile:text-3xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight tracking-tight mb-6 max-[424px]:mb-3 mobile:mb-3 md:mb-4 lg:mb-6">
            Always{' '}
            <span className="relative inline-block">
              <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                Learning
              </span>
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-xl opacity-15"
                initial={{ opacity: 0.15 }}
                animate={{
                  opacity: [0.15, 0.22, 0.15],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
                aria-hidden="true"
              />
            </span>
          </h2>
          <p className="text-mono-secondary text-lg max-[424px]:text-sm mobile:text-sm md:text-base lg:text-lg max-w-3xl mx-auto">
            What I'm working on to become a better frontend developer
          </p>
        </motion.div>

        {/* Learning areas */}
        <div className="space-y-6 max-[424px]:space-y-4 mobile:space-y-4 sm:space-y-4 md:space-y-5 lg:space-y-6">
          {learningAreas.map((area, index) => (
            <motion.div
              key={area.title}
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                duration: 0.6,
                delay: index * 0.15,
                type: "spring",
                stiffness: 100,
                damping: 20
              }}
              viewport={{ once: true, margin: "-50px" }}
              whileHover={{
                y: -4,
                transition: { type: "spring", stiffness: 400, damping: 25 }
              }}
              className="group bg-mono-surface/[0.08] border border-mono-border rounded-2xl max-[424px]:rounded-xl mobile:rounded-xl sm:rounded-xl md:rounded-xl lg:rounded-2xl p-8 max-[424px]:p-4 mobile:p-4 sm:p-5 md:p-6 lg:p-8 backdrop-blur-xl hover:bg-mono-surface/[0.12] hover:border-mono-accent/20 transition-all duration-300 cursor-pointer relative overflow-hidden"
            >
              {/* Subtle glow effect on hover */}
              <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                <div
                  className="absolute inset-0 rounded-2xl blur-xl"
                  style={{ backgroundColor: `${area.color}08` }}
                />
              </div>

              <div className="relative z-10 flex flex-col max-[424px]:flex-col mobile:flex-col sm:flex-col md:flex-row md:items-center gap-6 max-[424px]:gap-3 mobile:gap-3 sm:gap-4 md:gap-4 lg:gap-6">
                <div className="flex-1">
                  <div className="flex items-center gap-4 max-[424px]:gap-2 mobile:gap-2 sm:gap-3 md:gap-3 lg:gap-4 mb-4 max-[424px]:mb-2 mobile:mb-2 sm:mb-3 md:mb-3 lg:mb-4">
                    <h3 className="text-2xl max-[424px]:text-lg mobile:text-lg sm:text-lg md:text-xl lg:text-2xl font-bold text-mono-text group-hover:text-mono-accent transition-colors duration-300">
                      {area.title}
                    </h3>
                    <motion.span
                      className="px-4 py-2 max-[424px]:px-2 max-[424px]:py-1 mobile:px-2 mobile:py-1 sm:px-3 sm:py-1.5 md:px-3 md:py-1.5 lg:px-4 lg:py-2 rounded-full text-xs max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-xs font-semibold tracking-wide shadow-lg"
                      style={{
                        backgroundColor: `${area.color}15`,
                        color: area.color,
                        border: `1px solid ${area.color}30`
                      }}
                      whileHover={{ scale: 1.05 }}
                      transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    >
                      {area.timeframe}
                    </motion.span>
                  </div>
                  <p className="text-mono-secondary text-base max-[424px]:text-sm mobile:text-sm sm:text-sm md:text-sm lg:text-base leading-relaxed max-[424px]:leading-normal mobile:leading-normal group-hover:text-mono-secondary/90 transition-colors duration-300">
                    {area.description}
                  </p>
                </div>

                <div className="w-full max-[424px]:w-full mobile:w-full sm:w-full md:w-72 lg:w-72">
                  <div className="flex justify-between items-center mb-3 max-[424px]:mb-2 mobile:mb-2 sm:mb-2 md:mb-2 lg:mb-3">
                    <span className="text-mono-secondary text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm font-medium">Progress</span>
                    <motion.span
                      className="text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm font-bold"
                      style={{ color: area.color }}
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                      viewport={{ once: true }}
                    >
                      {area.progress}%
                    </motion.span>
                  </div>
                  <div className="relative h-3 max-[424px]:h-2 mobile:h-2 sm:h-2.5 md:h-2.5 lg:h-3 bg-mono-surface/20 rounded-full overflow-hidden shadow-inner">
                    <motion.div
                      className="h-full rounded-full relative overflow-hidden"
                      style={{ backgroundColor: area.color }}
                      initial={{ width: 0 }}
                      whileInView={{ width: `${area.progress}%` }}
                      transition={{
                        duration: 1.2,
                        delay: 0.2,
                        ease: "easeOut"
                      }}
                      viewport={{ once: true }}
                    >
                      {/* Animated shine effect */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                        initial={{ x: "-100%" }}
                        animate={{ x: "100%" }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          repeatDelay: 3,
                          ease: "easeInOut"
                        }}
                      />
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional skills tags */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-50px" }}
          className="mt-20 max-[424px]:mt-8 mobile:mt-8 sm:mt-12 md:mt-16 lg:mt-20 text-center"
        >
          <motion.h3
            className="text-xl max-[424px]:text-lg mobile:text-lg sm:text-lg md:text-lg lg:text-xl font-bold text-mono-text mb-8 max-[424px]:mb-4 mobile:mb-4 sm:mb-5 md:mb-6 lg:mb-8"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Additional Areas of Interest
          </motion.h3>
          <div className="flex flex-wrap justify-center gap-4 max-[424px]:gap-2 mobile:gap-2 sm:gap-3 md:gap-3 lg:gap-4 max-w-4xl mx-auto">
            {[
              'State Management',
              'API Integration',
              'Responsive Design',
              'Accessibility (a11y)',
              'Modern CSS Techniques'
            ].map((skill, index) => (
              <motion.span
                key={skill}
                initial={{ opacity: 0, scale: 0.8, y: 20 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                transition={{
                  duration: 0.5,
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 200,
                  damping: 20
                }}
                whileHover={{
                  scale: 1.05,
                  y: -2,
                  transition: { type: "spring", stiffness: 400, damping: 25 }
                }}
                viewport={{ once: true }}
                className="px-5 py-3 max-[424px]:px-3 max-[424px]:py-2 mobile:px-3 mobile:py-2 sm:px-4 sm:py-2.5 md:px-4 md:py-2.5 lg:px-5 lg:py-3 bg-mono-surface/10 border border-mono-border rounded-full text-mono-secondary text-sm max-[424px]:text-xs mobile:text-xs sm:text-xs md:text-xs lg:text-sm font-medium hover:bg-mono-surface/20 hover:border-mono-accent/30 hover:text-mono-text transition-all duration-300 cursor-pointer shadow-sm"
              >
                {skill}
              </motion.span>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
} 