'use client'
// import React from 'react'
import { motion } from 'framer-motion'
import { ArrowRight, CheckCircle, Globe, Clock } from 'lucide-react'
import { ScrollLink } from '@/components/ui/scroll-link'
import { useProjectCounts } from '@/hooks/use-project-stats'

// Contact stats data - Aligned with junior frontend developer experience
// Note: Projects count is now dynamic and will be updated in the component



// Animation variants
const transitionVariants = {
  item: {
    hidden: {
      opacity: 0,
      filter: 'blur(12px)',
      y: 12,
    },
    visible: {
      opacity: 1,
      filter: 'blur(0px)',
      y: 0,
      transition: {
        type: 'spring' as const,
        bounce: 0.3,
        duration: 1.5,
      },
    },
  },
}

export function ContactPreviewSection() {
  // Get dynamic project counts
  const projectCounts = useProjectCounts();

  // Dynamic contact stats with real-time project count
  const contactStats = [
    {
      value: '24h',
      label: 'Response Time',
      description: 'Quick email replies',
      color: '#00FF85',
      icon: <Clock />
    },
    {
      value: projectCounts.completedWithPlus,
      label: 'Projects Built',
      description: 'Learning through practice',
      color: '#1E90FF',
      icon: <CheckCircle />
    },
    {
      value: 'Remote',
      label: 'Availability',
      description: 'Open to opportunities',
      color: '#FF0099',
      icon: <Globe />
    }
  ];

  return (
    <section className="relative bg-mono-bg py-20 max-[424px]:py-16 mobile:py-16 sm:py-20 md:py-24 lg:py-32 overflow-hidden">
      {/* Enhanced premium background effects */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-1/3 left-1/4 w-[500px] h-[500px] rounded-full blur-[100px]"
          style={{
            background: 'radial-gradient(circle, rgba(207,111,244,0.08) 0%, rgba(166,81,225,0.04) 50%, transparent 100%)'
          }}
          animate={{
            y: [-15, 15],
            x: [-10, 10],
            opacity: [0.3, 0.1, 0.3],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-1/3 right-1/4 w-[600px] h-[600px] rounded-full blur-[120px]"
          style={{
            background: 'radial-gradient(circle, rgba(139,49,205,0.06) 0%, rgba(207,111,244,0.03) 50%, transparent 100%)'
          }}
          animate={{
            y: [15, -15],
            x: [10, -10],
            opacity: [0.1, 0.3, 0.1],
            scale: [1.1, 1, 1.1],
          }}
          transition={{
            duration: 14,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />

        {/* Subtle grid pattern */}
        <div
          className="absolute inset-0 opacity-[0.015]"
          style={{
            backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)',
            backgroundSize: '50px 50px'
          }}
        />
      </div>

      <div className="relative max-w-7xl mx-auto px-8 max-[424px]:px-8 mobile:px-8 sm:px-5 md:px-8 lg:max-w-4xl lg:px-6">
        {/* Section header */}
        <motion.div
          variants={transitionVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center mb-20 max-[424px]:mb-8 mobile:mb-8 sm:mb-12 md:mb-16 lg:mb-20"
        >
          {/* <motion.p
            className="text-mono-accent/80 text-sm max-[424px]:text-xs mobile:text-xs font-medium tracking-[0.25em] uppercase"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            LET'S COLLABORATE
          </motion.p> */}

          <motion.p
            className="text-mono-accent text-sm font-medium tracking-widest uppercase"
            initial={{ opacity: 0, y: 8 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1, duration: 0.5, ease: "easeOut" }}
            viewport={{ once: true }}
          >
            Work Together
          </motion.p>

          <motion.h2
            className="text-4xl max-[424px]:text-3xl mobile:text-3xl sm:text-4xl md:text-4xl lg:text-5xl font-bold text-mono-text leading-tight tracking-tight"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            viewport={{ once: true }}
          >
            Let's Build{' '}
            <span className="relative inline-block">
              <span className="relative z-10 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                Something
              </span>
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] via-[#A651E1] to-[#8B31CD] blur-2xl opacity-20"
                animate={{
                  opacity: [0.15, 0.25, 0.15],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />
            </span>
          </motion.h2>

          <motion.p
            className="text-lg md:text-base lg:text-lg text-mono-secondary max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
          </motion.p>
        </motion.div>

        {/* Contact Stats - Simplified Layout */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 max-[424px]:grid-cols-3 mobile:grid-cols-3 sm:grid-cols-3 md:grid-cols-3 gap-8 max-[424px]:gap-2 mobile:gap-2 sm:gap-3 md:gap-6 lg:gap-8 mb-20 max-[424px]:mb-8 mobile:mb-8 sm:mb-12 md:mb-16 lg:mb-20"
        >
          {contactStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.6,
                delay: 0.4 + index * 0.1,
                type: "spring",
                stiffness: 100,
                damping: 10
              }}
              viewport={{ once: true }}
              className="relative text-center p-8 max-[424px]:p-3 mobile:p-3 sm:p-4 md:p-6 lg:p-8 bg-mono-surface/[0.06] border border-mono-border/20 rounded-3xl max-[424px]:rounded-2xl mobile:rounded-2xl sm:rounded-2xl backdrop-blur-md group hover:bg-mono-surface/[0.1] hover:border-mono-border/40 transition-all duration-500 shadow-lg hover:shadow-xl"
              whileHover={{
                y: -6,
                scale: 1.03,
                transition: { duration: 0.4, ease: "easeOut" }
              }}
            >
              <div className="flex items-center justify-center mb-6 max-[424px]:mb-2 mobile:mb-2 sm:mb-3 md:mb-4 lg:mb-6">
                <motion.div
                  className="p-4 max-[424px]:p-2 mobile:p-2 sm:p-2.5 md:p-3 lg:p-4 rounded-2xl max-[424px]:rounded-xl mobile:rounded-xl sm:rounded-xl shadow-lg"
                  style={{ backgroundColor: `${stat.color}15`, color: stat.color }}
                  whileHover={{
                    scale: 1.15,
                    rotate: 8,
                    transition: { duration: 0.4, ease: "easeOut" }
                  }}
                >
                  <div className="w-5 h-5 max-[424px]:w-3 max-[424px]:h-3 mobile:w-3 mobile:h-3 sm:w-4 sm:h-4 md:w-4 md:h-4 lg:w-5 lg:h-5">
                    {stat.icon}
                  </div>
                </motion.div>
              </div>
              <motion.div
                className="text-4xl max-[424px]:text-xl mobile:text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-mono-text mb-3 max-[424px]:mb-1 mobile:mb-1 sm:mb-1.5 md:mb-2 lg:mb-3"
                whileHover={{
                  scale: 1.08,
                  transition: { duration: 0.3 }
                }}
              >
                {stat.value}
              </motion.div>
              <h3 className="text-mono-text font-semibold mb-2 max-[424px]:mb-0.5 mobile:mb-0.5 sm:mb-1 text-lg max-[424px]:text-xs mobile:text-xs sm:text-sm md:text-base lg:text-lg">{stat.label}</h3>
              <p className="text-mono-secondary text-sm max-[424px]:text-[10px] mobile:text-[10px] sm:text-xs leading-relaxed max-[424px]:leading-tight mobile:leading-tight">{stat.description}</p>

              {/* Enhanced card glow effect */}
              <motion.div
                className="absolute inset-0 opacity-0 rounded-3xl max-[424px]:rounded-2xl mobile:rounded-2xl sm:rounded-2xl"
                style={{ backgroundColor: stat.color }}
                whileHover={{
                  opacity: 0.03,
                  transition: { duration: 0.4 }
                }}
              />

              {/* Subtle inner glow */}
              <motion.div
                className="absolute inset-[1px] rounded-3xl max-[424px]:rounded-2xl mobile:rounded-2xl sm:rounded-2xl bg-gradient-to-b from-white/[0.02] to-transparent opacity-0"
                whileHover={{
                  opacity: 1,
                  transition: { duration: 0.4 }
                }}
              />
            </motion.div>
          ))}
        </motion.div>

        {/* Single CTA Button - Get in Touch */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <ScrollLink
            to="/contact"
            className="group relative overflow-hidden inline-flex items-center gap-3 max-[424px]:gap-2 mobile:gap-2 sm:gap-2.5 bg-gradient-to-r from-[#CF6FF4] to-[#8B31CD] text-white font-medium px-10 max-[424px]:px-6 mobile:px-6 py-4 max-[424px]:py-3 mobile:py-3 sm:px-6 sm:py-3 md:px-8 md:py-3 lg:px-10 lg:py-4 rounded-xl transition-all duration-500 hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <motion.div
              className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              initial={false}
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 3, repeat: Infinity }}
            />
            <span className="relative z-10 text-lg max-[424px]:text-base mobile:text-base sm:text-base md:text-base lg:text-lg font-semibold">Get in Touch</span>
            <motion.div
              className="relative"
              animate={{ x: [0, 5, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <ArrowRight className="w-5 h-5 max-[424px]:w-4 max-[424px]:h-4 mobile:w-4 mobile:h-4 sm:w-4 sm:h-4 md:w-4 md:h-4 lg:w-5 lg:h-5" />
            </motion.div>

            {/* Enhanced glow effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-[#CF6FF4] to-[#8B31CD] blur-xl opacity-0 group-hover:opacity-30 transition-opacity duration-500"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </ScrollLink>
        </motion.div>
      </div>
    </section>
  )
}
