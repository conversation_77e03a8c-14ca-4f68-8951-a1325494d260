/**
 * Constants and configuration for project data management
 * Centralized constants for consistent project categorization and styling
 */

import type { ProjectCategory, ProjectStatus, TechCategory } from './types'

// Project categories with display information
export const PROJECT_CATEGORIES: Record<ProjectCategory, {
  label: string
  description: string
  color: string
}> = {
  'Frontend': {
    label: 'Frontend',
    description: 'Client-side web applications and user interfaces',
    color: '#00FF85'
  },
  'Full-Stack': {
    label: 'Full-Stack',
    description: 'Complete web applications with frontend and backend',
    color: '#1E90FF'
  },
  'Mobile': {
    label: 'Mobile',
    description: 'Mobile applications and responsive web apps',
    color: '#FF0099'
  }
} as const

// Project status configurations
export const PROJECT_STATUS: Record<ProjectStatus, {
  label: string
  color: string
  bgColor: string
  description: string
}> = {
  'Completed': {
    label: 'Completed',
    color: '#00FF85',
    bgColor: 'bg-green-500/20',
    description: 'Project is finished and deployed'
  },
  'In Progress': {
    label: 'In Progress',
    color: '#1E90FF',
    bgColor: 'bg-blue-500/20',
    description: 'Currently working on this project'
  },
  'Planned': {
    label: 'Planned',
    color: '#FF0099',
    bgColor: 'bg-pink-500/20',
    description: 'Project is planned for future development'
  }
} as const

// Technology categories and their associated colors
export const TECHNOLOGY_CATEGORIES: Record<string, {
  category: TechCategory
  color: string
  icon?: string
}> = {
  // Frontend Technologies
  'React': { category: 'Frontend', color: '#61DAFB', icon: '⚛️' },
  'TypeScript': { category: 'Frontend', color: '#3178C6', icon: '📘' },
  'JavaScript': { category: 'Frontend', color: '#F7DF1E', icon: '🟨' },
  'HTML': { category: 'Frontend', color: '#E34F26', icon: '🌐' },
  'CSS': { category: 'Frontend', color: '#1572B6', icon: '🎨' },
  'Tailwind CSS': { category: 'Frontend', color: '#06B6D4', icon: '💨' },
  'Vite': { category: 'Frontend', color: '#646CFF', icon: '⚡' },
  'Next.js': { category: 'Frontend', color: '#000000', icon: '▲' },
  'shadcn/ui': { category: 'Frontend', color: '#000000', icon: '🎯' },
  'Framer Motion': { category: 'Frontend', color: '#0055FF', icon: '🎭' },
  
  // Backend Technologies
  'Node.js': { category: 'Backend', color: '#339933', icon: '🟢' },
  'Express': { category: 'Backend', color: '#000000', icon: '🚂' },
  'Prisma': { category: 'Backend', color: '#2D3748', icon: '🔺' },
  
  // Database Technologies
  'PostgreSQL': { category: 'Database', color: '#336791', icon: '🐘' },
  'Supabase': { category: 'Database', color: '#3ECF8E', icon: '⚡' },
  'MongoDB': { category: 'Database', color: '#47A248', icon: '🍃' },
  
  // APIs and Services
  'OpenWeather API': { category: 'API', color: '#FF6B35', icon: '🌤️' },
  'OpenWeatherMap API': { category: 'API', color: '#FF6B35', icon: '🌤️' },
  'Chart.js': { category: 'Frontend', color: '#FF6384', icon: '📊' },
  
  // Tools and Workflow
  'Git': { category: 'Tools', color: '#F05032', icon: '📝' },
  'GitHub': { category: 'Tools', color: '#181717', icon: '🐙' },
  'VS Code': { category: 'Tools', color: '#007ACC', icon: '💻' },
  'ESLint': { category: 'Tools', color: '#4B32C3', icon: '🔍' },
  'Prettier': { category: 'Tools', color: '#F7B93E', icon: '✨' },
  'Vercel': { category: 'Tools', color: '#000000', icon: '▲' },
  'React Router': { category: 'Frontend', color: '#CA4245', icon: '🛣️' }
} as const

// Default card styling configurations
export const DEFAULT_CARD_STYLES = {
  green: {
    background: 'from-[#0A2A1C] to-[#071812]',
    glow: 'from-[#00FF85]/10 to-transparent',
    accent: '#00FF85'
  },
  blue: {
    background: 'from-[#0A1A2A] to-[#071218]',
    glow: 'from-[#1E90FF]/10 to-transparent',
    accent: '#1E90FF'
  },
  pink: {
    background: 'from-[#2A0A1A] to-[#180712]',
    glow: 'from-[#FF0099]/10 to-transparent',
    accent: '#FF0099'
  },
  purple: {
    background: 'from-[#1A0A2A] to-[#120718]',
    glow: 'from-[#8B5CF6]/10 to-transparent',
    accent: '#8B5CF6'
  },
  orange: {
    background: 'from-[#2A1A0A] to-[#181207]',
    glow: 'from-[#F97316]/10 to-transparent',
    accent: '#F97316'
  }
} as const

// Featured projects configuration
export const FEATURED_PROJECTS_CONFIG = {
  maxFeaturedProjects: 3,
  defaultPriority: 100,
  autoFeatureThreshold: 90 // Projects with priority >= 90 are auto-featured
} as const

// Project validation rules
export const VALIDATION_RULES = {
  title: {
    minLength: 3,
    maxLength: 50
  },
  description: {
    minLength: 50,
    maxLength: 300
  },
  problemStatement: {
    minLength: 20,
    maxLength: 100
  },
  features: {
    minCount: 3,
    maxCount: 6
  },
  technologies: {
    minCount: 3,
    maxCount: 10
  },
  highlights: {
    minCount: 2,
    maxCount: 4
  },
  keywords: {
    minCount: 3,
    maxCount: 10
  }
} as const

// Default project template for new projects
export const DEFAULT_PROJECT_TEMPLATE = {
  status: 'Planned' as ProjectStatus,
  category: 'Frontend' as ProjectCategory,
  team: 'Solo project',
  isFeatured: false,
  isPublic: true,
  priority: FEATURED_PROJECTS_CONFIG.defaultPriority,
  cardStyle: DEFAULT_CARD_STYLES.blue,
  accentColor: DEFAULT_CARD_STYLES.blue.accent,
  keywords: ['React', 'TypeScript', 'Frontend'],
  highlights: ['Modern UI design', 'Responsive layout']
} as const

// Sorting configurations
export const SORT_CONFIGURATIONS = {
  priority: { label: 'Priority', defaultOrder: 'desc' as const },
  startDate: { label: 'Start Date', defaultOrder: 'desc' as const },
  title: { label: 'Title', defaultOrder: 'asc' as const },
  status: { label: 'Status', defaultOrder: 'asc' as const }
} as const
