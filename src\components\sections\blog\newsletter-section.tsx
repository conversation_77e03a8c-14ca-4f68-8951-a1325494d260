'use client'
import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Mail, Send, CheckCircle, ArrowRight, BookOpen, Bell, Users } from 'lucide-react'
import { Button } from '@/components/ui/button'

// Newsletter stats data
const newsletterStats = [
  {
    value: '2.5K+',
    label: 'Subscribers',
    description: 'Growing developer community',
    color: '#00FF85',
    icon: <Users className="w-4 h-4" />
  },
  {
    value: 'Weekly',
    label: 'Updates',
    description: 'Fresh content delivered',
    color: '#1E90FF',
    icon: <Bell className="w-4 h-4" />
  },
  {
    value: '95%',
    label: 'Open Rate',
    description: 'Highly engaged readers',
    color: '#FF0099',
    icon: <BookOpen className="w-4 h-4" />
  }
]

// Newsletter benefits
const benefits = [
  'Latest articles delivered to your inbox',
  'Exclusive tutorials and code snippets',
  'Early access to new content',
  'Developer tips and best practices',
  'No spam, unsubscribe anytime'
]

export function NewsletterSection() {
  const [email, setEmail] = useState('')
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) return

    setIsLoading(true)
    
    // Simulate API call
    setTimeout(() => {
      setIsSubscribed(true)
      setIsLoading(false)
      setEmail('')
    }, 1500)
  }

  return (
    <section id="newsletter" className="relative bg-mono-bg py-24 lg:py-32">
      <div className="max-w-7xl mx-auto px-6">
        <div className="max-w-4xl mx-auto">
          {/* Background gradient */}
          <div className="relative bg-gradient-to-br from-mono-surface/[0.08] to-mono-surface-dark/[0.12] border border-mono-border rounded-3xl p-8 lg:p-12 backdrop-blur-xl overflow-hidden">
            {/* Decorative background elements */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-accent-neon/5 to-transparent rounded-full blur-3xl" />
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-accent-electric/5 to-transparent rounded-full blur-3xl" />
            
            <div className="relative">
              {/* Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-12"
              >
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  viewport={{ once: true }}
                  className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-accent-neon to-accent-electric rounded-2xl mb-6"
                >
                  <Mail className="w-8 h-8 text-black" />
                </motion.div>
                
                <motion.h2 
                  className="text-3xl md:text-4xl font-bold text-mono-text mb-4 leading-tight tracking-tight"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  Stay{' '}
                  <span className="italic font-cursive text-transparent bg-clip-text bg-gradient-to-r from-accent-neon to-accent-electric">
                    Updated
                  </span>
                </motion.h2>
                
                <motion.p 
                  className="text-lg text-mono-secondary max-w-2xl mx-auto leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  Join thousands of developers who receive the latest articles, tutorials, 
                  and insights delivered directly to their inbox every week
                </motion.p>
              </motion.div>

              {/* Newsletter stats */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
                className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
              >
                {newsletterStats.map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                    viewport={{ once: true }}
                    className="text-center p-4 bg-mono-surface/[0.08] border border-mono-border/50 rounded-2xl backdrop-blur-sm"
                  >
                    <div className="flex items-center justify-center mb-2">
                      <div 
                        className="p-2 rounded-lg"
                        style={{ backgroundColor: `${stat.color}20`, color: stat.color }}
                      >
                        {stat.icon}
                      </div>
                    </div>
                    <div 
                      className="text-2xl font-bold mb-1"
                      style={{ color: stat.color }}
                    >
                      {stat.value}
                    </div>
                    <h3 className="text-mono-text font-semibold text-sm mb-1">
                      {stat.label}
                    </h3>
                    <p className="text-mono-secondary text-xs">
                      {stat.description}
                    </p>
                  </motion.div>
                ))}
              </motion.div>

              {/* Subscription form or success message */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                viewport={{ once: true }}
                className="text-center"
              >
                {isSubscribed ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                    className="space-y-4"
                  >
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-accent-neon/20 rounded-full mb-4">
                      <CheckCircle className="w-8 h-8 text-accent-neon" />
                    </div>
                    <h3 className="text-2xl font-bold text-mono-text mb-2">
                      Welcome to the Community!
                    </h3>
                    <p className="text-mono-secondary">
                      Thank you for subscribing. You'll receive your first newsletter soon.
                    </p>
                  </motion.div>
                ) : (
                  <div className="space-y-8">
                    {/* Subscription form */}
                    <form onSubmit={handleSubmit} className="max-w-md mx-auto">
                      <div className="flex flex-col sm:flex-row gap-4">
                        <input
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="Enter your email address"
                          required
                          className="flex-1 px-4 py-3 bg-mono-surface/30 border border-mono-border rounded-xl text-mono-text placeholder-mono-secondary focus:outline-none focus:border-accent-neon/50 focus:ring-2 focus:ring-accent-neon/20 transition-all duration-300"
                        />
                        <Button 
                          type="submit"
                          disabled={isLoading}
                          className="bg-gradient-to-r from-accent-neon to-accent-electric text-black font-semibold hover:from-accent-neon/90 hover:to-accent-electric/90 transition-all duration-300 px-8 py-3 h-auto disabled:opacity-50"
                        >
                          {isLoading ? (
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 border-2 border-black/30 border-t-black rounded-full animate-spin" />
                              Subscribing...
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              Subscribe
                              <Send className="w-4 h-4" />
                            </div>
                          )}
                        </Button>
                      </div>
                    </form>

                    {/* Benefits list */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto text-left">
                      {benefits.map((benefit, index) => (
                        <motion.div
                          key={benefit}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                          viewport={{ once: true }}
                          className="flex items-center gap-3 text-mono-secondary text-sm"
                        >
                          <CheckCircle className="w-4 h-4 text-accent-neon flex-shrink-0" />
                          {benefit}
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
