/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      screens: {
        'mobile': {'max': '425px'},
        'mobile': '425px',
        'sm-tablet': '640px',
        'tablet': '768px',
        'laptop-sm': '1024px',
        'laptop-md': '1280px',
        'laptop-lg': '1440px',
      },
      colors: {
        mono: {
          bg: 'rgb(var(--mono-bg))',
          text: 'rgb(var(--mono-text))',
          secondary: 'rgb(var(--mono-secondary))',
          border: 'rgb(var(--mono-border))',
          accent: 'rgb(var(--mono-accent))',
          'accent-light': 'rgb(var(--mono-accent-light))',
          'accent-dark': 'rgb(var(--mono-accent-dark))',
          surface: 'rgb(var(--mono-surface))',
          'surface-light': 'rgb(var(--mono-surface-light))',
          'surface-dark': 'rgb(var(--mono-surface-dark))',
        },
        accent: {
          neon: 'rgb(var(--accent-neon))',
          electric: 'rgb(var(--accent-electric))',
          vivid: 'rgb(var(--accent-vivid))',
        }
      },
      animation: {
        'fade-in': 'fade-in 0.5s ease-in-out',
        'fade-out': 'fade-out 0.5s ease-in-out',
        'slide-in': 'slide-in 0.5s ease-in-out',
        'slide-out': 'slide-out 0.5s ease-in-out',
      },
      keyframes: {
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'fade-out': {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        'slide-in': {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        'slide-out': {
          '0%': { transform: 'translateY(0)' },
          '100%': { transform: 'translateY(-100%)' },
        },
      },
      fontFamily: {
        cursive: ['Playfair Display', 'serif'],
      },
    },
  },
  plugins: [],
}