@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --mono-bg: 5, 5, 5;              /* #000000 - Pure black */
    --mono-text: 245, 245, 245;      /* #F5F5F5 - Almost white for maximum readability */
    --mono-secondary: 204, 204, 204;  /* #CCCCCC - Lighter secondary text */
    --mono-border: 102, 102, 102;    /* #666666 - More visible borders */
    --mono-accent: 170, 170, 170;    /* #AAAAAA - Brighter accent */
    --mono-accent-light: 187, 187, 187;  /* #BBBBBB - Lighter accent */
    --mono-accent-dark: 153, 153, 153;   /* #999999 - Darker accent */
    --mono-surface: 17, 17, 17;      /* #111111 - Subtle surface */
    --mono-surface-light: 26, 26, 26; /* #1A1A1A - Lighter surface */
    --mono-surface-dark: 8, 8, 8;    /* #080808 - Darker surface */

    /* New accent colors */
    --accent-neon: 0, 255, 133;      /* #00FF85 - Neon green */
    --accent-electric: 30, 144, 255;  /* #1E90FF - Electric blue */
    --accent-vivid: 255, 0, 153;     /* #FF0099 - Vivid pink */
  }
}

@layer base {
  * {
    @apply border-mono-border;
  }
  body {
    @apply bg-mono-bg text-mono-text;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Performance optimizations */
  * {
    /* Reduce layout thrashing */
    box-sizing: border-box;
  }

  /* Critical performance optimizations */
  .will-change-transform {
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
    transform-style: preserve-3d;
  }

  /* Optimize animations for 60fps */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Reduce animation overhead */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Optimize font loading */
  body {
    font-display: swap;
    text-rendering: optimizeSpeed;
  }
}

@layer utilities {
  /* Custom scrollbar for command palette */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #444444 transparent;
  }

  /* Webkit scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #444444;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #555555;
  }

  /* Hide scrollbar completely for clean minimal look */
  /* Firefox scrollbar */
  html {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  /* Webkit browsers (Chrome, Safari, Edge) - Hide scrollbar */
  ::-webkit-scrollbar {
    display: none;
  }

  /* Ensure scrolling still works */
  html, body {
    overflow-x: hidden;
    overflow-y: auto;
  }
}

/* Mobile breakpoint utilities */
@layer utilities {
  /* Small mobile: ≤424px */
  @media (max-width: 424px) {
    .mobile-sm-only\:block {
      display: block;
    }
    .mobile-sm-only\:hidden {
      display: none;
    }
    .mobile-sm-only\:text-xs {
      font-size: 0.75rem;
      line-height: 1rem;
    }
    .mobile-sm-only\:text-sm {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }
    .mobile-sm-only\:px-2 {
      padding-left: 0.5rem;
      padding-right: 0.5rem;
    }
    .mobile-sm-only\:py-1 {
      padding-top: 0.25rem;
      padding-bottom: 0.25rem;
    }
    .mobile-sm-only\:gap-2 {
      gap: 0.5rem;
    }
  }

  /* Standard mobile: 425px-639px (handled by default and mobile: prefix) */
  /* This range is covered by default styles (mobile-first) and mobile: prefix from Tailwind */
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.8s ease-in-out forwards;
  }

  .animate-wave {
    animation: wave 2.5s ease-in-out infinite;
    transform-origin: 70% 70%;
  }

  .font-cursive {
    font-family: 'Playfair Display', serif;
    font-style: italic;
  }

  /* Line clamp utility for text truncation */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes wave {
    0%, 100% {
      transform: rotate(0deg);
    }
    10%, 30% {
      transform: rotate(14deg);
    }
    20% {
      transform: rotate(-8deg);
    }
    40% {
      transform: rotate(14deg);
    }
    50% {
      transform: rotate(10deg);
    }
    60% {
      transform: rotate(0deg);
    }
  }
}