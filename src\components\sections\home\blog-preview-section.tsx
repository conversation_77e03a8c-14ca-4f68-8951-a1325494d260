'use client'

import React, { useRef } from 'react'
import { motion, useScroll, useTransform, useSpring } from 'framer-motion'
import { ArrowRight, Calendar, Clock, BookOpen, TrendingUp } from 'lucide-react'

import { ScrollLink } from '@/components/ui/scroll-link'

// Blog post interface
interface BlogPost {
  title: string
  excerpt: string
  date: string
  readTime: string
  category: string
  image: string
  accentColor: string
}

// Recent blog posts data
const recentPosts: BlogPost[] = [
  {
    title: 'Building Scalable React Applications with TypeScript',
    excerpt: 'Learn how to structure large React applications using TypeScript and best practices for maintainable code.',
    date: '2024-01-15',
    readTime: '8 min read',
    category: 'React',
    image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    accentColor: '#00FF85'
  },
  {
    title: 'Node.js Performance Optimization Strategies',
    excerpt: 'Discover proven techniques to optimize Node.js applications for better performance and scalability.',
    date: '2024-01-05',
    readTime: '10 min read',
    category: 'Node.js',
    image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    accentColor: '#1E90FF'
  },
  {
    title: 'Modern CSS Techniques for Better User Interfaces',
    excerpt: 'Explore the latest CSS features including Grid, Flexbox, and Container Queries for responsive design.',
    date: '2024-01-10',
    readTime: '6 min read',
    category: 'CSS',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    accentColor: '#FF0099'
  }
]

// Enhanced Blog Card Component with refined scroll animations
const BlogCard: React.FC<{ post: BlogPost; index: number }> = ({ post, index }) => {
  const cardRef = useRef<HTMLDivElement>(null)

  // Individual card scroll tracking with wider, smoother trigger zones
  const { scrollYProgress } = useScroll({
    target: cardRef,
    offset: ["start 0.9", "end 0.1"]
  })

  // Smoother card entrance and exit animations with gradual curves and spring damping
  const cardOpacity = useSpring(
    useTransform(scrollYProgress, [0, 0.25, 0.75, 1], [0, 1, 1, 0]),
    { stiffness: 60, damping: 20, restDelta: 0.001 }
  )

  const cardY = useSpring(
    useTransform(scrollYProgress, [0, 0.25, 0.75, 1], [40, 0, 0, -40]),
    { stiffness: 60, damping: 20, restDelta: 0.001 }
  )

  const cardScale = useSpring(
    useTransform(scrollYProgress, [0, 0.25, 0.75, 1], [0.95, 1, 1, 0.95]),
    { stiffness: 60, damping: 20, restDelta: 0.001 }
  )

  // More subtle rotation effect with gentler timing
  const cardRotateY = useSpring(
    useTransform(scrollYProgress, [0, 0.4, 0.6, 1], [
      index % 2 === 0 ? -0.2 : 0.2,
      0,
      0,
      index % 2 === 0 ? 0.2 : -0.2
    ]),
    { stiffness: 40, damping: 25, restDelta: 0.001 }
  )

  return (
    <motion.article
      ref={cardRef}
      style={{
        opacity: cardOpacity,
        y: cardY,
        scale: cardScale,
        rotateY: cardRotateY,
        transformStyle: "preserve-3d",
        willChange: "transform, opacity"
      }}
      className="group cursor-pointer"
      whileHover={{
        y: -4,
        transition: {
          type: "spring",
          stiffness: 400,
          damping: 25,
          mass: 0.8
        }
      }}
    >
      <ScrollLink to="/blog" className="block">
        <motion.div
          className="relative bg-mono-surface/[0.08] border border-mono-border rounded-2xl overflow-hidden backdrop-blur-xl transition-all duration-300"
          whileHover={{
            scale: 1.005,
            y: -2,
            transition: {
              type: "spring",
              stiffness: 300,
              damping: 30,
              mass: 0.6
            }
          }}
        >
          {/* Post image */}
          <div className="relative overflow-hidden">
            <motion.img
              src={post.image}
              alt={post.title}
              className="w-full h-48 object-cover"
              whileHover={{
                scale: 1.05,
                transition: {
                  type: "spring",
                  stiffness: 200,
                  damping: 20,
                  mass: 0.5
                }
              }}
            />
            <motion.div
              className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"
              whileHover={{
                background: "linear-gradient(to top, rgba(0,0,0,0.6), transparent)",
                transition: { duration: 0.3 }
              }}
            />
          </div>

          {/* Post content */}
          <div className="p-6 space-y-4">
            {/* Meta information */}
            <div className="flex items-center gap-4 text-mono-secondary text-sm">
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                {new Date(post.date).toLocaleDateString()}
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {post.readTime}
              </div>
            </div>

            {/* Title */}
            <motion.h3
              className="text-xl font-bold text-mono-text leading-tight"
              whileHover={{
                color: post.accentColor,
                transition: { duration: 0.3 }
              }}
            >
              {post.title}
            </motion.h3>

            {/* Excerpt */}
            <p className="text-mono-secondary leading-relaxed line-clamp-3">
              {post.excerpt}
            </p>

            {/* Footer */}
            <div className="flex items-center justify-between pt-2">
              <motion.span
                className="px-3 py-1 bg-mono-surface/30 border border-mono-border/50 rounded-full text-sm font-medium"
                style={{ color: post.accentColor }}
                whileHover={{
                  backgroundColor: `${post.accentColor}20`,
                  borderColor: `${post.accentColor}60`,
                  transition: { duration: 0.3 }
                }}
              >
                {post.category}
              </motion.span>
              <motion.div
                whileHover={{
                  x: 5,
                  transition: { duration: 0.3 }
                }}
              >
                <ArrowRight
                  className="w-5 h-5"
                  style={{ color: post.accentColor }}
                />
              </motion.div>
            </div>
          </div>

          {/* Enhanced card glow effect */}
          <motion.div
            className="absolute inset-0 opacity-0 rounded-2xl"
            style={{ backgroundColor: post.accentColor }}
            whileHover={{
              opacity: 0.03,
              scale: 1.05,
              transition: { duration: 0.3, ease: "easeOut" }
            }}
          />

          {/* Floating shadow */}
          <motion.div
            className="absolute inset-0 bg-mono-surface-dark/20 rounded-3xl blur-2xl -z-10"
            whileHover={{
              scale: 1.05,
              opacity: 0.3,
              transition: { duration: 0.3, ease: "easeOut" }
            }}
          />
        </motion.div>
      </ScrollLink>
    </motion.article>
  )
}



export function BlogPreviewSection() {
  const sectionRef = useRef<HTMLDivElement>(null)

  // Main section scroll progress tracking with refined offset
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start 0.9", "end 0.1"]
  })

  // Section-wide entrance and exit animations (fixed opacity to prevent gaps)
  const sectionOpacity = useSpring(
    useTransform(scrollYProgress, [0, 0.1, 0.9, 1], [1, 1, 1, 1]),
    { stiffness: 100, damping: 10 }
  )

  const sectionY = useSpring(
    useTransform(scrollYProgress, [0, 0.1, 0.9, 1], [0, 0, 0, 0]),
    { stiffness: 100, damping: 10 }
  )

  // Refined background parallax effects with reduced intensity for smoother performance
  const backgroundY1 = useSpring(
    useTransform(scrollYProgress, [0, 1], [-12, 12]),
    { stiffness: 50, damping: 25, restDelta: 0.001 }
  )
  const backgroundY2 = useSpring(
    useTransform(scrollYProgress, [0, 1], [8, -8]),
    { stiffness: 50, damping: 25, restDelta: 0.001 }
  )

  const backgroundScale1 = useSpring(
    useTransform(scrollYProgress, [0, 0.5, 1], [0.98, 1.01, 1.02]),
    { stiffness: 40, damping: 30, restDelta: 0.001 }
  )
  const backgroundScale2 = useSpring(
    useTransform(scrollYProgress, [0, 0.5, 1], [1.02, 0.99, 0.98]),
    { stiffness: 40, damping: 30, restDelta: 0.001 }
  )

  // Gentler rotation for smoother movement without glitches
  const backgroundRotate1 = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, 30]),
    { stiffness: 30, damping: 35, restDelta: 0.001 }
  )
  const backgroundRotate2 = useSpring(
    useTransform(scrollYProgress, [0, 1], [30, 0]),
    { stiffness: 30, damping: 35, restDelta: 0.001 }
  )

  // Smoother opacity changes with spring damping
  const backgroundOpacity1 = useSpring(
    useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.04, 0.08, 0.08, 0.04]),
    { stiffness: 60, damping: 20, restDelta: 0.001 }
  )
  const backgroundOpacity2 = useSpring(
    useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.03, 0.06, 0.06, 0.03]),
    { stiffness: 60, damping: 20, restDelta: 0.001 }
  )

  return (
    <motion.section
      ref={sectionRef}
      className="relative bg-mono-bg py-32 overflow-hidden"
      style={{
        opacity: sectionOpacity,
        y: sectionY,
        willChange: "transform, opacity"
      }}
    >
      {/* Solid background overlay to prevent gaps */}
      <div className="absolute inset-0 bg-mono-bg -z-10" />
      {/* Enhanced background effects with sophisticated parallax */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-1/4 left-1/4 w-[600px] h-[600px] bg-mono-surface/8 rounded-full blur-[120px]"
          style={{
            y: backgroundY1,
            scale: backgroundScale1,
            rotate: backgroundRotate1,
            opacity: backgroundOpacity1,
            willChange: "transform, opacity"
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-[800px] h-[800px] bg-mono-surface-light/8 rounded-full blur-[130px]"
          style={{
            y: backgroundY2,
            scale: backgroundScale2,
            rotate: backgroundRotate2,
            opacity: backgroundOpacity2,
            willChange: "transform, opacity"
          }}
        />

        {/* Refined floating element for depth with smoother animations */}
        <motion.div
          className="absolute top-1/2 left-1/2 w-[300px] h-[300px] bg-gradient-to-r from-[#FF0099]/3 to-[#8B31CD]/3 rounded-full blur-[60px] -translate-x-1/2 -translate-y-1/2"
          style={{
            y: useSpring(
              useTransform(scrollYProgress, [0, 1], [6, -6]),
              { stiffness: 40, damping: 30, restDelta: 0.001 }
            ),
            scale: useSpring(
              useTransform(scrollYProgress, [0, 0.5, 1], [0.95, 1.01, 0.95]),
              { stiffness: 35, damping: 35, restDelta: 0.001 }
            ),
            opacity: useSpring(
              useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0.015, 0.04, 0.04, 0.015]),
              { stiffness: 50, damping: 25, restDelta: 0.001 }
            ),
            willChange: "transform, opacity"
          }}
        />
      </div>

      <div className="relative max-w-7xl mx-auto px-6">
        {/* Section header */}
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 5 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.3,
              ease: "easeOut"
            }}
            viewport={{ once: true, margin: "-50px" }}
            className="space-y-3"
          >
            <p className="text-mono-accent text-sm font-medium tracking-widest uppercase">
              LATEST INSIGHTS
            </p>
            <h2 className="text-3xl md:text-5xl font-bold text-mono-text leading-tight tracking-tight">
              From the{' '}
              <span className="relative inline-block">
                <span className="relative z-10 bg-gradient-to-r from-[#FF0099] to-[#8B31CD] text-transparent bg-clip-text italic font-cursive">
                  Blog
                </span>
                <motion.span
                  className="absolute inset-0 bg-gradient-to-r from-[#FF0099] to-[#8B31CD] blur-xl opacity-20"
                  animate={{
                    opacity: [0.15, 0.2, 0.15],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />
              </span>
            </h2>
          </motion.div>
        </div>

        {/* Blog stats */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.4,
            ease: "easeOut"
          }}
          viewport={{ once: true, margin: "-50px" }}
        >
          {[
            { icon: BookOpen, value: "25+", label: "Articles", description: "Technical deep dives", color: "#00FF85" },
            { icon: TrendingUp, value: "10K+", label: "Readers", description: "Monthly visitors", color: "#1E90FF" },
            { icon: Calendar, value: "Weekly", label: "Updates", description: "Fresh content", color: "#FF0099" }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.6,
                delay: 0.5 + index * 0.1,
                type: "spring",
                stiffness: 100,
                damping: 10
              }}
              viewport={{ once: true, margin: "-50px" }}
              className="relative text-center p-6 bg-mono-surface/[0.08] border border-mono-border rounded-2xl backdrop-blur-xl group cursor-default"
              whileHover={{
                y: -3,
                scale: 1.01,
                transition: {
                  type: "spring",
                  stiffness: 300,
                  damping: 25,
                  mass: 0.7
                }
              }}
            >
              <div className="flex items-center justify-center mb-4">
                <motion.div
                  className="p-3 rounded-xl"
                  style={{ backgroundColor: `${stat.color}20`, color: stat.color }}
                  whileHover={{
                    scale: 1.05,
                    rotate: 3,
                    transition: {
                      type: "spring",
                      stiffness: 400,
                      damping: 20,
                      mass: 0.4
                    }
                  }}
                >
                  <stat.icon className="w-5 h-5" />
                </motion.div>
              </div>
              <motion.div
                className="text-3xl font-bold text-mono-text mb-2"
                whileHover={{
                  scale: 1.02,
                  transition: {
                    type: "spring",
                    stiffness: 350,
                    damping: 25,
                    mass: 0.5
                  }
                }}
              >
                {stat.value}
              </motion.div>
              <h3 className="text-mono-text font-semibold mb-1">{stat.label}</h3>
              <p className="text-mono-secondary text-sm">{stat.description}</p>

              {/* Card glow effect */}
              <motion.div
                className="absolute inset-0 opacity-0 rounded-2xl"
                style={{ backgroundColor: stat.color }}
                whileHover={{
                  opacity: 0.05,
                  transition: { duration: 0.3 }
                }}
              />
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced posts grid with scroll animations */}
        <motion.div
          className="grid lg:grid-cols-3 gap-8 mb-16"
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.4,
            ease: "easeOut"
          }}
          viewport={{ once: true, margin: "-50px" }}
        >
          {recentPosts.map((post, index) => (
            <BlogCard key={post.title} post={post} index={index} />
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <ScrollLink
            to="/blog"
            className="group relative overflow-hidden inline-flex items-center gap-3 bg-transparent border-2 border-mono-border/30 hover:border-accent-neon/50 text-mono-text font-medium px-8 py-4 rounded-xl transition-all duration-500"
          >
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-accent-neon/5 to-accent-electric/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              initial={false}
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 3, repeat: Infinity }}
            />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-accent-neon/10 to-accent-electric/10 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500"
            />
            <span className="relative z-10 text-base">Read All Articles</span>
            <motion.div
              className="relative"
              animate={{ x: [0, 5, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <ArrowRight className="w-4 h-4" />
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-accent-neon to-accent-electric blur-lg opacity-0 group-hover:opacity-50"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </motion.div>
          </ScrollLink>
        </motion.div>
      </div>
    </motion.section>
  )
}
